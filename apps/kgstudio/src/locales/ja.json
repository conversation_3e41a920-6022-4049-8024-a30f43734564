{"common.action": "アクション", "common.actions": "[アクション]", "common.add": "追加", "common.admin": "管理者", "common.amount": "数量", "common.anonymous": "匿名", "common.any-chain": "", "common.any-token": "任意のトークン", "common.blockchain": "ブロックチェーン", "common.cancel": "[キャンセル]", "common.checking": "チェック中", "common.clear-all": "[すべてクリア]", "common.confirm": "確認", "common.confirmation": "[確認]", "common.connect-wallet-to-login": "ウォレットを接続してログインしてください", "common.contact-us": "お問い合わせ", "common.copy": "[コピー]", "common.created-at": "作成日", "common.delete": "[削除]", "common.edit": "[編集]", "common.email": "電子メール", "common.enter-email": "メールを入力", "common.enter-phone-number": "電話番号を入力", "common.error": "[エラー]", "common.go-back": "戻る", "common.hide-filter": "フィルターを非表示", "common.learn-more-about": "についてさらに詳しく", "common.loading": "読み込み中", "common.member": "メンバー", "common.name": "名前", "common.no-access": "アクセスなし", "common.no-data-available": "データはありません", "common.no-image": "", "common.owner": "オーナー", "common.owners": "オーナー", "common.phone-number": "電話番号", "common.pick-date": "日付を選択", "common.please-check": "チェックしてください", "common.please-wait": "しばらくお待ちください", "common.privacy-policy": "プライバシーポリシー", "common.recipient": "受信者", "common.refresh": "リフレッシュ", "common.remove": "[削除]", "common.role": "役割", "common.save": "[保存]", "common.search-placeholder": "クイックサーチ", "common.select": "[選択]", "common.select-chain": "チェーンを選択", "common.select-status": "ステータスを選択", "common.select-token": "トークンを選択", "common.send": "[送信]", "common.send-token": "トークンを送信", "common.show-filter": "フィルターを表示", "common.status.active": "アクティブ", "common.status.expired": "期限切れ", "common.status.inactive": "無効化", "common.status.pending": "保留中", "common.status.text": "ステータス", "common.status.title": "ステータス", "common.terms": "規約", "common.time": "時間", "common.time-utc8": "時刻 (UTC+8)", "common.token": "トークン", "common.tx-hash": "Tx ハッシュ", "common.tx-status-failed": "失敗", "common.tx-status-pending": "保留中", "common.tx-status-success": "成功", "common.update": "[更新]", "common.user": "ユーザ", "common.wallet": "ウォレットビルダー", "common.wallet-address": "ウォレットアドレス", "common.welcome": "ようこそ!", "error.cant-find-user": "申し訳ございません、このユーザーを見つけることができません。", "error.general-error": "何らかのエラーが発生しました。コード: ", "error.no-access": "アクセス権がありません。認証済みのウォレットアドレスを使用してログインしてください。", "error.try-again": "エラーが発生しました。後でもう一度試してください。", "error.user-not-found": "ユーザーが見つかりません。", "kgauth.change.change-email": "Eメールを変更", "kgauth.change.change-phone-number": "電話番号を変更", "kgauth.change.email-different": "新しいEメールは古いEメールと異なる必要があります", "kgauth.change.email-exists": "Eメールはすでに存在しています", "kgauth.change.email-update-failed": "Eメールを更新できませんでした。後でもう一度試してください。", "kgauth.change.new-email": "新しい電子メール", "kgauth.change.new-phone-number": "新しい電話番号", "kgauth.change.old-email": "あなたの古いメールは", "kgauth.change.old-phone-number": "あなたの古い電話番号は", "kgauth.change.password-input": "[パスワード]", "kgauth.change.password-input2": "[パスワードを確認]", "kgauth.change.password-input-hint1": "12 桁以上", "kgauth.change.password-input-hint2": "大文字と小文字、数字と文字を含む", "kgauth.change.password-mismatch": "パスワードは一致する必要があります", "kgauth.change.password-title": "パスワードを変更", "kgauth.change.phone-duplicate": "新しい電話番号は古い電話番号と異なる必要があります", "kgauth.change.phone-exists": "電話番号はすでに存在しています", "kgauth.change.verify-email": "Eメールを確認", "kgauth.change.verify-phone-number": "電話番号を確認", "kgauth.common.accept": "承諾する", "kgauth.common.change-email-address": "Eメールアドレスを変更", "kgauth.common.change-password": "パスワードを変更", "kgauth.common.change-phone-number": "電話番号を変更", "kgauth.common.continue": "続行", "kgauth.common.decline": "拒否", "kgauth.common.done": "完了", "kgauth.common.link-line-account": "LINE アカウントをリンクする", "kgauth.common.loading": "データ処理中です。このウィンドウは閉じないでください。", "kgauth.common.login": "ログイン", "kgauth.common.oauth-login": "<PERSON><PERSON>uth <PERSON>", "kgauth.common.resend": "[再送信]", "kgauth.common.retry": "リトライ", "kgauth.common.seconds": "秒", "kgauth.common.verify-email-address": "Eメールアドレスを確認", "kgauth.common.verify-phone-number": "電話番号を確認", "kgauth.errors.common": "何かがおかしくなった！", "kgauth.errors.common-retry": "何かがおかしくなった。後でもう一度試してください。", "kgauth.errors.invalid-email": "Eメールが無効です", "kgauth.errors.invalid-otp": "コードが無効です。もう一度試してください。", "kgauth.errors.invalid-phone": "電話番号が無効です", "kgauth.errors.otp-digit": "OTP は 6 桁である必要があります", "kgauth.errors.password-required": "パスワードが間違っています。", "kgauth.errors.rate-limit": "レート制限を超えました。後でもう一度試してください。", "kgauth.errors.return-to-wallet-in": "のウォレットに戻る", "kgauth.errors.return-to-wallet-now": "今すぐウォレットに戻る", "kgauth.errors.token-expired": "セッションの有効期限が切れました。続けるには再度ログインしてください。", "kgauth.errors.update-phone": "電話番号を更新できませんでした。後でもう一度試してください。", "kgauth.forgot.backup-seed": "バックアップシードフレーズ", "kgauth.forgot.desc": "重要:「OK」をクリックしてパスワードをリセットすると、同時にアカウントの資産情報が消去されます。心配はいりません。後でシードフレーズを使ってアセットをインポートできます。ただし、最初にシードフレーズをバックアップすることをおすすめします。", "kgauth.forgot.password": "パスワードを忘れた", "kgauth.forgot.reset": "大丈夫。ログアウトしてパスワードをリセットします。", "kgauth.forgot.step1": "アプリからログアウトする", "kgauth.forgot.step2": "ログインし直す", "kgauth.forgot.step3": "「パスワードを忘れた」をクリックし、ガイドに従ってください", "kgauth.forgot.step-title": "「OK」をクリックして、手順に従ってください", "kgauth.forgot.sub-title": "続行するには、ログアウトしてください。パスワードのリセットをお手伝いします。", "kgauth.login.email": "電子メール", "kgauth.login.input-required": "ログイン/登録情報が必要です。", "kgauth.login.or": "または", "kgauth.login.phone": "電話", "kgauth.login.signin-sub-title": "続けて", "kgauth.login.signin-title": "KryptoGO でログイン/登録する。", "kgauth.login.with-google": "グーグルでログイン", "kgauth.oauth-callback.authorize-description": "次の権限が必要です。この機密情報を確認し、このサイトまたはアプリと共有することに同意することを確認してください。", "kgauth.oauth-callback.authorize-item-0": "メールアドレス、電話番号、ユーザーハンドルなどの KryptoGO アカウント情報を表示します", "kgauth.oauth-callback.authorize-item-1": "ウォレットアドレスにアクセスしてデジタル資産を表示する", "kgauth.oauth-callback.authorize-item-2": "取引履歴を表示します", "kgauth.oauth-callback.authorize-item-3": "", "kgauth.oauth-callback.authorize-scope-and": "そして", "kgauth.oauth-callback.authorize-scope-asset": "アセット、NFT、トランザクション", "kgauth.oauth-callback.authorize-scope-chatroom": "チャットルーム", "kgauth.oauth-callback.authorize-scope-edit": "編集する", "kgauth.oauth-callback.authorize-scope-notification": "通知", "kgauth.oauth-callback.authorize-scope-order": "注文", "kgauth.oauth-callback.authorize-scope-read": "見る", "kgauth.oauth-callback.authorize-scope-token": "トークン", "kgauth.oauth-callback.authorize-scope-transaction": "取引", "kgauth.oauth-callback.authorize-scope-user": "ユーザー情報", "kgauth.oauth-callback.authorize-scope-vault": "跳馬", "kgauth.oauth-callback.authorize-scope-wallet": "財布", "kgauth.oauth-callback.authorize-scope-your": "きみの", "kgauth.oauth-callback.subtitle": "アプリケーションが KryptoGO アカウントへのアクセスを求めています", "kgauth.oauth-callback.title": "アプリを認証", "kgauth.password.enter-password": "パスワードを入力", "kgauth.password.forget-password": "パスワードをお忘れですか？", "kgauth.password.invalid": "パスワードが無効です。もう一度試してください。", "kgauth.password.sub-title": "ウォレット資産にアクセスするには、パスワードを使用してバックアップからウォレットを復元してください。", "kgauth.success.email": "Eメールが正常に確認されました！", "kgauth.success.email-updated": "Eメールが正常に更新されました！", "kgauth.success.password-updated": "パスワードは正常に更新されました！", "kgauth.success.phone": "電話番号が正常に確認されました！", "kgauth.success.phone-updated": "電話番号の更新が完了しました。", "kgauth.verify.enter-digit": "受信した6桁のコードを入力してください", "kgauth.verify.not-receive-code": "コードが届きませんでしたか？", "kgauth.verify.title": "にコードを送信しました", "kgform.common.back-to-home": "ホームに戻る", "kgform.common.change": "[変更]", "kgform.common.next-page": "次のページ", "kgform.common.previous-page": "前のページ", "kgform.common.resend": "再送信", "kgform.common.start": "[開始]", "kgform.common.submit": "送信", "kgform.common.verify": "検証", "kgform.errors.addres.in-use": "このアドレスは既に使用されています。", "kgform.errors.address.in-use": "このアドレスは既に使用されています。", "kgform.errors.address.invalid-format": "住所の形式が無効です。", "kgform.errors.birth-date": "18歳以上である必要があります。", "kgform.errors.code.invalid": "コードが無効です。", "kgform.errors.code.invalid-format": "OTP コードは 6 桁でなければなりません。", "kgform.errors.email.in-use": "このメールはすでに使用されています。", "kgform.errors.email.invalid-format": "Eメールの形式が無効です。", "kgform.errors.file-upload.file-too-large": "ファイルサイズが大きすぎます。", "kgform.errors.form-validation-error": "一部のフィールドは正しく入力されていません。", "kgform.errors.idIssue-date": "発行日を正確に入力してください。", "kgform.errors.id-number": "正しい ID 番号を入力してください。", "kgform.errors.max-length": "{maxLength}最大長は文字です", "kgform.errors.no-client-id": "OAuth クライアント ID が無効です。", "kgform.errors.oauth-login-error": "ログインできませんでした。", "kgform.errors.phone.in-use": "電話番号はすでに使用されています。", "kgform.errors.phone.invalid-format": "電話番号の形式が無効です。", "kgform.errors.privacy-policy": "個人データのセキュリティとプライバシーポリシーに同意してください", "kgform.errors.required": "このフィールドは必須です", "kgform.errors.signature.empty": "フルネームで署名してください。", "kgform.errors.signature.invalid": "署名が無効です。", "kgform.errors.something-went-wrong": "問題が発生しました。後でもう一度試してください。", "kgform.errors.submission-error": "送信エラーです。後でもう一度試してください。", "kgform.errors.too-many-request": "リクエストが多すぎます。後でもう一度試してください。", "kgform.errros.code.invalid": "コードが無効です。", "kgform.form.address.connect": "接続", "kgform.form.address.disconnect": "接続解除", "kgform.form.email.dialog.description": "確認コードはに送信されました {email}", "kgform.form.email.dialog.title": "E メール検証", "kgform.form.email.in-use": "このメールはすでに使用されています。", "kgform.form.file-upload.choose-file": "[ファイルを選択]", "kgform.form.file-upload.start-over": "最初からやり直す", "kgform.form.login-and-start": "ログインして開始", "kgform.form.phone.dialog.description": "確認コードはに送信されました {phone}", "kgform.form.phone.dialog.title": "電話認証", "kgform.form.signature.clear": "クリア", "kgform.form.signature.dialog.title": "サイン", "kgform.form.signature.placeholder": "ここをクリックしてフルネームに署名してください", "kgform.form.signature.save": "[保存]", "kgform.form.successfully-verirfied": "正常に検証されました。", "kgform.form.upload-placeholder": "クリックまたはドラッグしてファイルをアップロード", "kgform.index.buy-crypto": "クリプトを購入", "kgform.index.login": "ログイン", "kgform.index.register": "登録", "kgstore.checkout.cta": "支払いを済ませました", "kgstore.checkout.customer-info": "顧客情報", "kgstore.checkout.error-create-order": "注文を作成できませんでした。", "kgstore.checkout.order-correct-text": "注文が作成されると、指定された期限内にお支払いを行うことができます。", "kgstore.checkout.order-correct-title": "注文は正しいですか？", "kgstore.checkout.order-summary": "注文概要", "kgstore.checkout.rate-updated-text": "選択したアイテムは、新しいレートで更新されました。もう一度試してください。", "kgstore.checkout.rate-updated-title": "新しいレートが更新されました", "kgstore.checkout.receiving-wallet": "受け取りウォレット", "kgstore.checkout.title": "チェックアウト", "kgstore.checkout.toast": "{time}お支払い期限は注文確認後となります。", "kgstore.checkout.transfer-fund": "への資金移動 ", "kgstore.checkout.will-receive": "お届けします", "kgstore.common.about": "について", "kgstore.common.back": "戻る", "kgstore.common.bank-transfer": "銀行振込", "kgstore.common.buy": "購入", "kgstore.common.cancel": "[キャンセル]", "kgstore.common.confirm": "確認", "kgstore.common.copy-success": "コピーが成功しました!", "kgstore.common.email": "電子メール", "kgstore.common.error": "何かがおかしくなった。後でもう一度試してください。", "kgstore.common.error-get-wallet": "ウォレット情報を取得できませんでした。", "kgstore.common.kyc-status.pending": "保留中", "kgstore.common.kyc-status.processing": "処理", "kgstore.common.kyc-status.rejected": "拒否", "kgstore.common.kyc-status.unverified": "未確認", "kgstore.common.kyc-status.verified": "検証済み", "kgstore.common.limit": "リミット", "kgstore.common.login-now": "今すぐログイン", "kgstore.common.login-order-desc": "ログインして注文する", "kgstore.common.login-order-title": "ログインして注文する", "kgstore.common.next": "[次へ]", "kgstore.common.payment-method": "支払い方法", "kgstore.common.phone": "電話", "kgstore.common.rate": "レート", "kgstore.common.recaptcha-error": "Google 再キャプチャの検証に失敗しました。ウェブページを更新するか、サポートチームにお問い合わせください。", "kgstore.common.submit": "送信", "kgstore.common.subtotal": "小計", "kgstore.common.total": "合計", "kgstore.common.try-again": "もう一度試す", "kgstore.login.back": "戻る", "kgstore.login.check-email-code": "Eメールをチェックして、OTPコードを受け取ってください。", "kgstore.login.continue": "続行", "kgstore.login.email-required": "Eメールが必要です", "kgstore.login.otp-message": "OTP は 6 桁である必要があります", "kgstore.login.phone-required": "電話番号は必須です", "kgstore.login.resend": "[再送信]", "kgstore.login.success": "ログインに成功しました。リダイレクトしていますので、しばらくお待ちください。", "kgstore.login.title": "ログイン/ KryptoGOに登録して続けてください", "kgstore.login.welcome": "ようこそ!", "kgstore.menu.orders": "注文", "kgstore.menu.product": "[製品]", "kgstore.menu.profile": "プロフィール", "kgstore.order.active-title": "アクティブ", "kgstore.order.attachments.count": "[{count}ファイル]", "kgstore.order.attachments.title": "添付ファイル", "kgstore.order.cancelled-time": "キャンセル時間", "kgstore.order.cancel-toast": "{orderId}所有者が注文をキャンセルし、合計支払い額はになりました{cost}。\n\nすでに支払いを済ませている場合は、できるだけ早く所有者に連絡して払い戻しを受けてください。", "kgstore.order.cancel-toast-cta": "連絡先情報を確認する", "kgstore.order.delivered-time": "お届け日時", "kgstore.order.error-get-order": "アクティブな注文を取得できませんでした", "kgstore.order.error-get-order-details": "注文の詳細を取得できませんでした", "kgstore.order.history-title": "歴史", "kgstore.order.no-order": "まだ有効な注文または完了した注文はありません", "kgstore.order.no-order-history": "注文はまだ完了していません。", "kgstore.order.order-created": "注文が作成されました", "kgstore.order.payment-time": "支払い時期", "kgstore.order.receiving-wallet": "受け取りウォレット", "kgstore.order.shipment-time": "発送時間", "kgstore.order.shipment-tx-hash": "トランザクションハッシュ", "kgstore.order.shipping-time": "配送時間", "kgstore.order.status-awaiting-confirmation": "確認待ち", "kgstore.order.status-awaiting-shipment": "発送待ち", "kgstore.order.status-canceled": "キャンセルされました", "kgstore.order.status-delivered": "配信済み", "kgstore.order.status-shipping": "配送", "kgstore.order.status-unpaid": "未払い", "kgstore.order.unpaid-toast": "{order}事前にお支払いください{time}。", "kgstore.order.view-more": "もっと見る", "kgstore.order.view-order-detail": "注文の詳細を表示", "kgstore.order.view-receipt": "領収書を表示", "kgstore.payment.desc": "支払いを証明するために、少なくとも1つのスクリーンショット、画像、またはその他の添付ファイルをアップロードしてください。", "kgstore.payment.error-attachment": "添付ファイルをアップロードできませんでした", "kgstore.payment.error-file-size": "ファイルサイズが大きすぎてアップロードできません。", "kgstore.payment.error-file-type": "ファイルタイプが無効です。使用できるのは png、jpg、jpeg、および webp だけです。", "kgstore.payment.error-max-upload": "添付できる添付ファイルは 10 個だけです。", "kgstore.payment.error-upload": "支払い詳細をアップロードできませんでした", "kgstore.payment.file-restrict": "png/jpg/jpeg/heic/heif をここにドロップ\nファイルサイズの上限は 10 MB です。\n画像の縦横比:300*8000 ピクセル", "kgstore.payment.status-awaiting-refund": "払い戻し待ち", "kgstore.payment.status-paid": "支払い済み", "kgstore.payment.status-refunded": "払い戻し", "kgstore.payment.success-upload": "支払い詳細が正常にアップロードされました！", "kgstore.payment.title": "添付ファイルをアップロード", "kgstore.payment.upload-and-inform": "アップロードして出品者に通知", "kgstore.payment.x-files-uploaded": "アップロードされたファイル", "kgstore.product.account-name": "アカウント名", "kgstore.product.account-number": "口座番号", "kgstore.product.bank-name": "銀行名", "kgstore.product.branch-name": "ブランチ", "kgstore.product.buy-now": "今すぐ購入", "kgstore.product.cta-need-kyc": "KYCを申請して続行", "kgstore.product.cta-sign-in": "サインインして購入", "kgstore.product.current-rate": "現在のレート", "kgstore.product.error-max-amount": "最大金額は", "kgstore.product.error-max-quantity": "最大数量は", "kgstore.product.error-merchant": "マーチャント情報を取得できませんでした。", "kgstore.product.error-min-amount": "最小金額は", "kgstore.product.error-min-quantity": "最小数量は", "kgstore.product.fee-desc-1": "合計金額にはすべての手数料が含まれます。実際の購入数量は次のように計算されます。", "kgstore.product.fee-desc-2": "購入数量の手数料が最低額よりも低い場合は、それに応じて合計金額が調整されます。", "kgstore.product.fee-formula-1": "合計/為替レート。", "kgstore.product.fee-formula-2": "合計/ (為替レート* (1+比例手数料)。", "kgstore.product.handling-fee": "取扱手数料", "kgstore.product.include-fee": "{fee_percentage}価格には手数料% が含まれています", "kgstore.product.introduction": "はじめに", "kgstore.product.limit": "リミット", "kgstore.product.market-profile": "マーケットプロファイル", "kgstore.product.minimum-handling-fee": "最低手数料", "kgstore.product.no-handling-fee": "手数料なし", "kgstore.product.no-product": "所有者は製品を公開していません。", "kgstore.product.receive": "そして受け取る", "kgstore.product.spend": "使いたい", "kgstore.product.title": "製品", "kgstore.product.transfer-funds": "への資金移動", "kgstore.profile.title": "プロフィール", "kgstore.toast.guest-desc": "すぐにログインして本人確認を行うと、気に入った商品が見つかったときにいつでも注文できます。", "kgstore.toast.guest-title": "最も安全な暗号交換プラットフォームをお楽しみください", "kgstore.toast.pending-title": "お客様の身分証明書申請は審査中です。", "kgstore.toast.rejected-desc": "詳細については、カスタマーサポートにお問い合わせください。", "kgstore.toast.rejected-title": "本人確認申請は却下されました。", "kgstore.toast.unverified-title": "本人確認をして取引を開始してください。", "kgstore.toast.verified-desc": "本人確認申請書が確認されました！これで注文できます。", "kgstore.toast.verified-title": "本人確認に成功しました！", "kgstore.unpaid.cta": "支払いを済ませました", "kgstudio.asset.amount-invalid-description-1": "金額を調整するか、管理者に連絡して少なくとも入金してください", "kgstudio.asset.amount-invalid-description-2": "そして", "kgstudio.asset.amount-invalid-description-3": "取引を確実に行えるようにするためです。", "kgstudio.asset.available-balance": "利用可能な残高: ", "kgstudio.asset.balance": "資産残高", "kgstudio.asset.balance-checking": "残高確認中...", "kgstudio.asset.balance-value": "バランス: {formattedTokenBalance}", "kgstudio.asset.checking-kya": "ウォレットアドレスの潜在的なリスクのチェック", "kgstudio.asset.checking-kyc": "KYC ステータスの確認", "kgstudio.asset.checklist.contact-hint": "今日の残高:$0 (1日の送金限度額:$0) システム管理者またはオーナーが送金限度額を設定していないようです。送金限度額の設定については、担当者に連絡してサポートを受けてください。", "kgstudio.asset.checklist.edit-hint": "送金限度額を編集する", "kgstudio.asset.checklist.exceed-hint": "振り込める金額を超えています。金額を減らしてください。(今日の送金限度額残高:${remainLimit})", "kgstudio.asset.checklist.remain-hint": "本日の残高:$ {remainLimit}(1日の送金限度額:${dailyTransferLimit})", "kgstudio.asset.checklist.reuqired-approval": "この取引は確認する必要があります。(承認基準額:${threshold})", "kgstudio.asset.checklist.reuqired-hint": "金額は0より大きくなければなりません。{remainLimit}(今日の残高:$)", "kgstudio.asset.check-tx": "取引を確認してください", "kgstudio.asset.customer-transfer-time": "支払い通知時期", "kgstudio.asset.deposit-now": "今すぐ入金", "kgstudio.asset.edit-now": "今すぐ編集", "kgstudio.asset.edit-order-modal.accepted-file-types": "受け付けるファイル形式は png、jpg、jpeg、webp のみです。", "kgstudio.asset.edit-order-modal.cancel-transaction-instruction": "取引を停止したい場合は、この注文をキャンセルしてください。", "kgstudio.asset.edit-order-modal.change-status-to": "ステータスを変更", "kgstudio.asset.edit-order-modal.edit-unpaid-himt": "お客様から提供された支払い証明書に基づいて、次の情報を入力してください", "kgstudio.asset.edit-order-modal.file-size-error": "ファイルサイズが 10 MB を超えています", "kgstudio.asset.edit-order-modal.max-files-error": "最大ファイル数を超えました", "kgstudio.asset.edit-order-modal.max-file-upload-info": "最大10個のファイルをアップロードでき、各ファイルサイズは1MBを超えません", "kgstudio.asset.edit-order-modal.payment-amount-mismatch": "実際のお支払い金額は注文金額と異なります", "kgstudio.asset.edit-order-modal.payment-done-hint": "お客様は支払いを完了しました。速やかに出荷を手配してください。この取引を受け付けない場合は、注文をキャンセルした後、速やかに返金処理を行う必要があります。", "kgstudio.asset.edit-order-modal.payment-note": "支払いメモ", "kgstudio.asset.edit-order-modal.title": "支払い詳細を編集", "kgstudio.asset.edit-order-modal.upload-attachments": "添付ファイルをアップロード", "kgstudio.asset.edit-tx-note-modal.attachments.title": "添付ファイル", "kgstudio.asset.edit-tx-note-modal.note.hint": "トランザクションノートを入力し、承認に必要な添付ファイルをアップロードします。", "kgstudio.asset.edit-tx-note-modal.note.placeholder": "トランザクションノート", "kgstudio.asset.edit-tx-note-modal.note.title": "トランザクションノート", "kgstudio.asset.edit-tx-note-modal.title": "[編集]", "kgstudio.asset.edit-tx-note-modal.update-failed-error": "翻訳プレースホルダー", "kgstudio.asset.estimated-gas": "推定ガス料金:", "kgstudio.asset.estimated-gas-checking": "推定料金:確認中...", "kgstudio.asset.finance.loading": "ダッシュボードデータを生成中...", "kgstudio.asset.gas-insufficient": "ガストークンの残高が不十分です。", "kgstudio.asset.kya-info.total-balance": "資産残高", "kgstudio.asset.kya-info.total-received": "受領総額", "kgstudio.asset.kya-info.total-spent": "総支出額", "kgstudio.asset.kya-info.total-transactions": "合計取引", "kgstudio.asset.kya-status.atm.desc": "暗号通貨ATMオペレーター。", "kgstudio.asset.kya-status.atm.name": "ATM", "kgstudio.asset.kya-status.child-exploitation.desc": "追加情報はありません。", "kgstudio.asset.kya-status.child-exploitation.name": "児童搾取", "kgstudio.asset.kya-status.dark-market.desc": "ダークネットを介して運営され、違法な商品を暗号通貨で取引するために使用されるオンラインマーケットプレイス。", "kgstudio.asset.kya-status.dark-market.name": "ダークネットマーケットプレイス", "kgstudio.asset.kya-status.dark-service.desc": "ダークネットを介して運営され、暗号通貨の違法なサービスを提供する組織。", "kgstudio.asset.kya-status.dark-service.name": "ダークネットサービス", "kgstudio.asset.kya-status.enforcement-action.desc": "企業は法的手続きの対象となります。管轄区域にはサブタイプとして注釈が付けられます。", "kgstudio.asset.kya-status.enforcement-action.name": "執行措置", "'kgstudio.asset.kya-status.error'": "現在、このアドレスのリスクスキャンを実行できません。後でもう一度試してください。", "kgstudio.asset.kya-status.error": "現在、このアドレスのリスクスキャンを実行できません。後でもう一度試してください。", "kgstudio.asset.kya-status.exchange-fraudulent.desc": "違法行為に関与した取引所。", "kgstudio.asset.kya-status.exchange-fraudulent.name": "不正取引", "kgstudio.asset.kya-status.exchange-licensed.desc": "企業は、保管、交換、仲介、またはその他の関連する金融サービスを含む、暗号資産固有のビジネスライセンスを保有しています。参加者が中央機関 (エンティティ) とやり取りする交換サービスを提供します。具体的でない金融サービスのライセンスや、FATFと非協力的と記載されている管轄区域は含まれません。", "kgstudio.asset.kya-status.exchange-licensed.name": "交換:ライセンス済み", "kgstudio.asset.kya-status.exchange-unlicensed.desc": "このエンティティは暗号資産に関する特定のビジネスライセンスを保有しておらず、参加者が中央当事者（エンティティ）とやり取りする交換サービスを提供しています。これには認可を受けた事業体が含まれますが、FATFと非協力的と記載されている法域に所属しています。", "kgstudio.asset.kya-status.exchange-unlicensed.name": "交換:無ライセンス", "kgstudio.asset.kya-status.gambling.desc": "暗号通貨を使用してギャンブルサービスを提供するオンラインリソース。", "kgstudio.asset.kya-status.gambling.name": "ギャンブル", "kgstudio.asset.kya-status.high": "ハイ", "kgstudio.asset.kya-status.high-risk": "ハイリスク", "kgstudio.asset.kya-status.illegal-service.desc": "違法なサービスを提供したり、違法な活動に従事したりするリソース。", "kgstudio.asset.kya-status.illegal-service.name": "違法サービス", "kgstudio.asset.kya-status.liquidity-pools.desc": "追加情報はありません。", "kgstudio.asset.kya-status.liquidity-pools.name": "流動性プール", "kgstudio.asset.kya-status.low": "低い", "kgstudio.asset.kya-status.low-risk": "低リスク", "kgstudio.asset.kya-status.marketplace.desc": "法的サービスを提供したり、暗号通貨で商品を取引したりする団体。", "kgstudio.asset.kya-status.marketplace.name": "オンラインマーケットプレイス", "kgstudio.asset.kya-status.medium": "ミディアム", "kgstudio.asset.kya-status.medium-risk": "中程度のリスク", "kgstudio.asset.kya-status.miner.desc": "計算能力を利用して暗号通貨ブロックのマイニングを行う組織。", "kgstudio.asset.kya-status.miner.name": "マイナー", "kgstudio.asset.kya-status.mixer.desc": "さまざまな資金源からの資金を組み合わせて、追跡を困難にしたり、ほとんど不可能にしたりするサービス。主にマネーロンダリングに使用されます。", "kgstudio.asset.kya-status.mixer.name": "ミキシングサービス", "kgstudio.asset.kya-status.not-enough-info": "この住所には、リスク分析に必要な情報が不足しています。", "kgstudio.asset.kya-status.other.desc": "追加情報はありません。", "kgstudio.asset.kya-status.other.name": "その他の信頼できる情報源", "kgstudio.asset.kya-status.others.desc": "追加情報はありません。", "kgstudio.asset.kya-status.others.name": "その他", "kgstudio.asset.kya-status.p2p-exchange-licensed.desc": "企業は、保管、交換、仲介、またはその他の関連する金融サービスを含む、暗号資産固有のビジネスライセンスを保有しています。参加者が互いに直接交換する交換サービスを提供します。非特定の金融サービスライセンスや、FATFと非協力的と記載されている管轄区域は含まれません。", "kgstudio.asset.kya-status.p2p-exchange-licensed.name": "P2P エクスチェンジ:ライセンス取得済み", "kgstudio.asset.kya-status.p2p-exchange-unlicensed.desc": "法人は暗号資産の特定の事業ライセンスを保有しておらず、参加者が直接交換する交換サービスを提供しています。これには認可を受けた事業体も含まれますが、FATFとは協力的ではないと記載されている法域に所在します。", "kgstudio.asset.kya-status.p2p-exchange-unlicensed.name": "P2P エクスチェンジ:ライセンスなし", "kgstudio.asset.kya-status.payment.desc": "顧客と決済サービスを提供する会社との間の仲介役として機能するサービス。", "kgstudio.asset.kya-status.payment.name": "支払いプロセッサ", "kgstudio.asset.kya-status.potential-risk": "住所の潜在的リスク", "kgstudio.asset.kya-status.ransom.desc": "暗号通貨による支払いを要求する強要業者。", "kgstudio.asset.kya-status.ransom.name": "身代金恐喝者", "kgstudio.asset.kya-status.sanctions.desc": "追加情報はありません。", "kgstudio.asset.kya-status.sanctions.name": "制裁", "kgstudio.asset.kya-status.scam.desc": "顧客を騙して仮想通貨を乗っ取った法人", "kgstudio.asset.kya-status.scam.name": "詐欺", "kgstudio.asset.kya-status.seized-assets.desc": "追加情報はありません。", "kgstudio.asset.kya-status.seized-assets.name": "押収資産", "kgstudio.asset.kya-status.source.suspicious": "疑わしい情報源", "kgstudio.asset.kya-status.source.trusted": "信頼できる情報源", "kgstudio.asset.kya-status.stolen-coins.desc": "ハッキングによって他人の暗号通貨を乗っ取ったエンティティ。", "kgstudio.asset.kya-status.stolen-coins.name": "盗まれたコイン", "kgstudio.asset.kya-status.terrorist-financing.desc": "追加情報はありません。", "kgstudio.asset.kya-status.terrorist-financing.name": "テロ資金調達", "kgstudio.asset.kya-status.view-potential-risk-details": "潜在的なリスクの詳細を見る", "kgstudio.asset.kya-status.wallet-address-risk": "ウォレットアドレスリスク", "kgstudio.asset.kya-status.wallet.desc": "暗号通貨で保管および支払いを行うためのサービス。", "kgstudio.asset.kya-status.wallet.name": "オンラインウォレット", "kgstudio.asset.kya-status.wallet-risk-checkin": "ウォレットのリスクを分析中...", "kgstudio.asset.kyc-status.scan": "リスクスキャン", "kgstudio.asset.kyc-status.scan-address": "ウォレットアドレスリスク", "kgstudio.asset.kyc-status.subtitle": "転送する前にユーザー情報を確認してください。", "kgstudio.asset.market-profile.email": "電子メール", "kgstudio.asset.market-profile.intro": "はじめに", "kgstudio.asset.market-profile.intro-tooltip": "店舗の上部に表示されています。", "kgstudio.asset.market-profile.line-id": "ライン ID", "kgstudio.asset.market-profile.logo": "[ロゴ]", "kgstudio.asset.market-profile.phone": "電話", "kgstudio.asset.market-profile.section-title": "マーケットプロファイル", "kgstudio.asset.market-profile.store-link": "プレビュー", "kgstudio.asset.market-profile.title": "タイトル", "kgstudio.asset.market-profile.title-tooltip": "Web サイトのタイトルに表示されます。", "kgstudio.asset.market-profile.url": "URL", "kgstudio.asset.market-profile.url-tooltip": "ドメインをカスタマイズするには、サポートにお問い合わせください。", "kgstudio.asset.mismatch-error": "このユーザーは、選択したチェーン ({selectedBlockchain}) からウォレットをインポートしていません。あなたが送金した金額は相手に見えないかもしれません。", "kgstudio.asset.note-attachments-required": "トランザクションノートを入力し、承認に必要な添付ファイルをアップロードします。承認なしで送金する場合は、金額を下げるか、しきい値を編集してください。", "kgstudio.asset.order-detail.action-card.remain-balance": "今日の残高:{balance}(1日の送金限度額:{limit})", "kgstudio.asset.order-detail.action-card.tx-failed": "{txHash}トランザクションの送信が失敗しました。もう一度試してください。({shippedAt})", "kgstudio.asset.order-detail.actions.awaiting-confirmation": "お客様から支払いが通知されました。できるだけ早く確認してください。", "kgstudio.asset.order-detail.actions.cancel-order": "注文をキャンセル", "kgstudio.asset.order-detail.actions.confirmation-hint": "マネーロンダリングを防ぐため、ユーザーの登録銀行口座が送金口座と一致していることを確認して、それが本人の取引であることを確認してください。一致しない場合は、キャンセルして返金することができます。", "kgstudio.asset.order-detail.actions.mark-as-paid": "支払い済みとしてマークする", "kgstudio.asset.order-detail.actions.order-cancelled": "注文はキャンセルされました。", "kgstudio.asset.order-detail.actions.order-done": "注文は完了しました。", "kgstudio.asset.order-detail.actions.order-shipping": "現在輸送中です。Shipping Proofで配送の進捗状況をリアルタイムで確認してください。", "kgstudio.asset.order-detail.actions.payment-deadline": "購入者は事前に支払う必要があります。{deadline}そうでない場合は、この注文をキャンセルできます。\n\n顧客が支払いを行う前に取引を承認しない場合、返金処理を行わずに注文をキャンセルできます。", "kgstudio.asset.order-detail.actions.title": "[アクション]", "kgstudio.asset.order-detail.cancel-modal.cancel": "この注文をキャンセル", "kgstudio.asset.order-detail.cancel-modal.description": "内部メモに理由を記入してください。ユーザーは支払いを完了しました。この注文をキャンセルしたら、できるだけ早く返金を処理する必要があります。", "kgstudio.asset.order-detail.cancel-modal.title": "この注文をキャンセルしてもよろしいですか？", "kgstudio.asset.order-detail.edit-internal-note": "内部メモを編集", "kgstudio.asset.order-detail.order-details": "注文詳細", "kgstudio.asset.order-detail.order-information": "注文情報", "kgstudio.asset.order-detail.order-information.customer": "顧客", "kgstudio.asset.order-detail.order-information.order-time": "注文時間", "kgstudio.asset.order-detail.order-information.payment-details": "支払い詳細", "kgstudio.asset.order-detail.order-information.product": "[製品]", "kgstudio.asset.order-detail.order-information.qty": "数量", "kgstudio.asset.order-detail.order-information.title": "注文情報", "kgstudio.asset.order-detail.order-information.total-price": "合計金額", "kgstudio.asset.order-detail.order-information.tx-id": "注文番号", "kgstudio.asset.order-detail.payment-details.account-info": "銀行名:{bankName}\n支店名:{branchName}\n口座番号:{accountNumber}\n口座名義人名:{accountHolderName}", "kgstudio.asset.order-detail.payment-details.account-info-less-content": "{bankName}{branchName}\n口座番号:{accountNumber}\n口座名義人名:{accountHolderName}", "kgstudio.asset.order-detail.payment-details.confirmed-as-paid": "この注文は支払い済みであることが確認されました。", "kgstudio.asset.order-detail.payment-details.customer-account": "ユーザーが登録した個人銀行口座情報（詳細については、ユーザーKYCの詳細を参照してください）：", "kgstudio.asset.order-detail.payment-details.info-from-customer": "顧客からの支払い情報", "kgstudio.asset.order-detail.payment-details.last-five-digits": "実際の支払い口座番号の下5桁", "kgstudio.asset.order-detail.payment-details.title": "支払い詳細", "kgstudio.asset.order-detail.payment-details.unpaid": "お客様はまだ支払いを通知していません。", "kgstudio.asset.order-detail.payment-details.unpaid-time-reminder": "{deadline}支払い期限はあり、支払い期限までにはまだ{timeLeft}残っています。", "kgstudio.asset.order-detail.summary.internal-note": "内部メモ", "kgstudio.asset.order-detail.summary.internal-note-hint": "注文処理状況、注文キャンセルの理由、返金情報、進捗状況などの詳細を入力します。", "kgstudio.asset.order-detail.summary.payment-status": "支払い状況", "kgstudio.asset.order-detail.summary.process-by": "処理者", "kgstudio.asset.order-detail.summary.shipment-status": "配送状況", "kgstudio.asset.order-detail.summary.shipping-proof": "配送証明", "kgstudio.asset.order-detail.summary.title": "サマリー", "kgstudio.asset.order-settings.payment-terms": "支払い条件", "kgstudio.asset.order-settings.payment-terms-tooltip": "注文が確定すると、支払い期限のリマインダーが自動的に表示されます。ただし、期限切れの注文はご自身でキャンセルする必要があります。", "kgstudio.asset.order-settings.section-title": "注文設定", "kgstudio.asset.orders.just now": "たった今", "kgstudio.asset.orders.just-now": "たった今", "kgstudio.asset.orders.list.customer": "顧客", "kgstudio.asset.orders.list.order-created": "注文が作成されました", "kgstudio.asset.orders.list.order-id": "注文 ID", "kgstudio.asset.orders.list.order-purchase": "購入", "kgstudio.asset.orders.list.order-status": "注文状況", "kgstudio.asset.orders.list.payment": "支払", "kgstudio.asset.orders.list.shipment": "出荷", "kgstudio.asset.orders.list.total-price": "合計金額", "kgstudio.asset.orders.search.placeholder": "注文 ID、顧客", "kgstudio.asset.orders.steps.awaiting-payment": "支払い待ち", "kgstudio.asset.orders.steps.awaiting-shipment": "発送待ち", "kgstudio.asset.orders.steps.order-cancelled": "注文がキャンセルされました", "kgstudio.asset.orders.steps.order-completed": "注文完了", "kgstudio.asset.orders.steps.order-created": "注文が作成されました", "kgstudio.asset.orders.steps.paid": "支払い済み", "kgstudio.asset.orders.steps.sent": "送信済み", "kgstudio.asset.orders.steps.shipping": "配送", "kgstudio.asset.order-status.awaiting-confirmation": "確認待ち", "kgstudio.asset.order-status.awaiting-shipment": "発送待ち", "kgstudio.asset.order-status.cancelled": "キャンセルされました", "kgstudio.asset.order-status.delivered": "配信済み", "kgstudio.asset.order-status.shipping": "配送", "kgstudio.asset.order-status.unpaid": "未払い", "kgstudio.asset.orders.title": "注文", "kgstudio.asset.payment-info.account-holder-name": "口座名義人の名前", "kgstudio.asset.payment-info.account-number": "口座番号", "kgstudio.asset.payment-info.bank-name": "銀行名", "kgstudio.asset.payment-info.bank-transfer": "銀行振込", "kgstudio.asset.payment-info.branch-name": "支店名", "kgstudio.asset.payment-info.currency": "支払い通貨", "kgstudio.asset.payment-info.payment-method": "支払い方法", "kgstudio.asset.payment-info.section-title": "支払い情報", "kgstudio.asset.payment-status.awaiting-refund": "払い戻し待ち", "kgstudio.asset.payment-status.paid": "支払い済み", "kgstudio.asset.payment-status.refunded": "払い戻し", "kgstudio.asset.payment-status.unpaid": "未払い", "kgstudio.asset.products.action": "アクション", "kgstudio.asset.products.available": "利用可能", "kgstudio.asset.products.cancel-modal-cancel": "はい、キャンセル", "kgstudio.asset.products.cancel-modal-hint": "キャンセルしてもよろしいですか？編集したばかりのデータは保存されません。", "kgstudio.asset.products.cancel-modal-stay": "いいえ、泊まって", "kgstudio.asset.products.edit-product": "製品を編集", "kgstudio.asset.products.fee": "手数料", "kgstudio.asset.products.fee-free-reminder-description": "例:10,000{quoteCurrency}ドルを使うと、(10000/{currentPrice}＝{quoteAmount}{baseCurrency}({chain}) が受け取れます", "kgstudio.asset.products.fee-free-reminder-title": "合計金額 = 価格 × トークン金額", "kgstudio.asset.products.fee-included-reminder-description": "例:10,000ドルを使うと、{quoteCurrency}(10000/{quoteDenominator}/{currentPrice}) = {quoteAmount}{baseCurrency}({chain}) が付与されます", "kgstudio.asset.products.fee-included-reminder-title": "合計金額 = (1 +「手数料」) × 価格 × トークン金額", "kgstudio.asset.products.fee-included-reminder-with-min-fee-description": "例:10,000{quoteCurrency}ドルを使うと、(10000-{minFee})/{currentPrice}＝{quoteAmount}{baseCurrency}({chain}) が受け取れます", "kgstudio.asset.products.fee-included-reminder-with-min-fee-title": "合計金額 = 最低手数料+ 価格 × トークン金額", "kgstudio.asset.products.handling-fee": "取扱手数料", "kgstudio.asset.products.handling-fee-hint": "最小値:0%、最大値:100%", "kgstudio.asset.products.handling-fee-no": "手数料なし", "kgstudio.asset.products.handling-fee-proportional": "手数料 (価格に追加)", "kgstudio.asset.products.handling-fee-yes": "手数料あり", "kgstudio.asset.products.image-size": "サイズ:200*200 ピクセル", "kgstudio.asset.products.inventory-greater-than-zero": "ディスプレイインベントリはゼロより大きくなければなりません。", "kgstudio.asset.products.inventory-less": "ディスプレイインベントリは1億,000,000未満である必要があります。", "kgstudio.asset.products.limit-from-required": "注文制限が必要です", "kgstudio.asset.products.limit-to-required": "注文制限が必要です", "kgstudio.asset.products.limit-validation": "注文制限範囲は有効な数値でなければなりません", "kgstudio.asset.products.minimum-fee": "最低手数料", "kgstudio.asset.products.name-required": "製品名が必要です", "kgstudio.asset.products.not-ready-for-publish-description": "公開する前に製品設定の編集を完了してください", "kgstudio.asset.products.not-ready-for-publish-title": "商品情報が設定されていないため、公開できません。", "kgstudio.asset.products.order-limit": "注文限度額", "kgstudio.asset.products.order-limits": "注文制限", "kgstudio.asset.products.price": "価格", "kgstudio.asset.products.price-required": "製品価格が必要です", "kgstudio.asset.products.product-name": "[製品名]", "kgstudio.asset.products.product-type": "製品タイプ", "kgstudio.asset.products.product-type-buy-crypto": "クリプトを購入", "kgstudio.asset.products.publish": "パブリッシュ", "kgstudio.asset.products.reset-all": "復元", "kgstudio.asset.products.reset-all-title": "情報を以前の設定に戻す", "kgstudio.asset.products.reset-image": "[イメージをリセット]", "kgstudio.asset.products.reset-to-default": "デフォルトにリセット", "kgstudio.asset.products.status-published": "公開済み", "kgstudio.asset.products.status-unpublished": "未発行", "kgstudio.asset.products.stock": "インベントリ", "kgstudio.asset.products.stock-hint": "アセットプロ・トレジャリーの在庫状況:{tokenAmount}", "kgstudio.asset.products.trading-pair": "トレーディングペア", "kgstudio.asset.products.updated": "更新済み", "kgstudio.asset.products.update-failure-toast": "「{baseCurrency}/{quoteCurrency}({chain})」を更新できませんでした。もう一度試してください。(エラーコード:{code})", "kgstudio.asset.products.update-success-toast": "「{baseCurrency}/{quoteCurrency}({chain})」は正常に更新されました。", "kgstudio.asset.recipient-address": "受取人 (ウォレットアドレス)", "kgstudio.asset.recipient-send-by": "受信者 (送信者{sendType})", "kgstudio.asset.remain-limit-checking": "1 日の残りの転送上限:確認中...", "kgstudio.asset.remain-limit-info": "1日の残りの送金限度額：$ {remainLimit}of $ {dailyTransferLimit}。", "kgstudio.asset.remain-limit-info-invalid": "現在、取引を送信することはできません。また、管理者に連絡して送金限度額を調整することもできません。 ", "kgstudio.asset.remain-limit-invalid-hint": "金額を調整するか、管理者に連絡して送金限度額を調整してください。 ", "kgstudio.asset.threshold-info": "この取引には承認が必要です (承認基準額:$未満{transferApprovalThreshold})。", "kgstudio.asset.token-and-gas-insufficient": "トークンとガスの残高が不十分です。", "kgstudio.asset.token-insufficient": "トークンの残高が不十分です。", "kgstudio.asset.transfer-amount": "送金金額", "kgstudio.asset.transfer-to": "転送先", "kgstudio.asset.transfer-validation": "送信前に問題を修正してください。", "kgstudio.asset.transfer-validation-amount-invalid": "転送トークンの金額が無効です。", "kgstudio.asset.transfer-validation-info-invalid": "承認には取引メモと添付ファイルが必要です", "kgstudio.asset.transfer-validation-recipient-invalid": "受信者が必要です。取引の前に「リスクスキャン」をクリックしてください。", "kgstudio.asset.tx-action-card.approve": "承認", "kgstudio.asset.tx-action-card.awaiting-approval.msg-approver": "取引の詳細を確認して、取引を承認するか拒否するかを決定してください。", "kgstudio.asset.tx-action-card.awaiting-approval.msg-normal": "このトランザクションは承認待ちです。プロセスを迅速に進めたい場合は、承認者に連絡することを検討してください。", "kgstudio.asset.tx-action-card.awaiting-release.msg-finance-manager": "トランザクションの詳細を慎重に検討し、トランザクションを実行用にリリースするか、適宜拒否してください。", "kgstudio.asset.tx-action-card.awaiting-release.msg-normal": "このトランザクションはリリース待ちです。このプロセスを迅速に進めたい場合は、財務マネージャーに連絡することを検討してください。", "kgstudio.asset.tx-action-card.reject": "拒否", "kgstudio.asset.tx-action-card.rejected.review-note.title": "レビューメモ", "kgstudio.asset.tx-action-card.rejected.title": "この取引は拒否されました ❌", "kgstudio.asset.tx-action-card.release": "[リリース]", "kgstudio.asset.tx-action-card.release-check-list.balance-enough": "利用可能な残高:十分 ({tokenBalance} {tokenSymbol})", "kgstudio.asset.tx-action-card.release-check-list.balance-loading": "組織の残高の確認", "kgstudio.asset.tx-action-card.release-check-list.balance-not-enough.desc": "承認者がこの取引を承認する前に、少なくともを補充してください {shortfallAmount}{tokenSymbol}（{chainName}）。", "kgstudio.asset.tx-action-card.release-check-list.balance-not-enough.title": "利用可能な残高:不十分です ({tokenBalance} {tokenSymbol})", "kgstudio.asset.tx-action-card.release-check-list.fee-enough": "推定転送ガス料金:{fee}{tokenSymbol}", "kgstudio.asset.tx-action-card.release-check-list.fee-loading": "組織の残りのネイティブトークンを確認する", "kgstudio.asset.tx-action-card.release-check-list.fee-not-enough.desc": "承認者がこの取引を承認する前に、少なくとも を補充してください {shortfallAmount} {tokenSymbol}({chainName})。", "kgstudio.asset.tx-action-card.release-check-list.fee-not-enough.title": "利用可能なネイティブトークンの残高:不十分です。 ", "kgstudio.asset.tx-action-card.send-failed.msg": "詳細については、下のボタンをクリックすると、ブロックチェーンエクスプローラーのWebサイトにリダイレクトされます。", "kgstudio.asset.tx-action-card.send-failed.title": "トランザクションが失敗しました ❌", "kgstudio.asset.tx-action-card.sending.msg": "このトランザクションはリリースされ、送信中です。ブロックチェーンが混雑していると、予想以上に時間がかかる場合がありますのでご注意ください。\n\n詳細については、下のボタンをクリックすると、ブロックチェーンエクスプローラーのウェブサイトにリダイレクトされます。", "kgstudio.asset.tx-action-card.send-success.msg": "詳細については、下のボタンをクリックすると、ブロックチェーンエクスプローラーのWebサイトにリダイレクトされます。", "kgstudio.asset.tx-action-card.send-success.title": "この取引は完了しました ✅", "kgstudio.asset.tx-action-card.title": "アクション", "kgstudio.asset.tx-approval-modal.insufficient-balance-error.desc": "管理者が十分に入金してから再試行してください {shortfallAmount} {tokenSymbol} ({chainName})", "kgstudio.asset.tx-approval-modal.insufficient-balance-error.title": "エラー:{tokenSymbol}({chainName}) インベントリが不十分", "kgstudio.asset.tx-approval-modal.success.title": "正常に承認されました", "kgstudio.asset.tx-confirm-approval-modal.approve": "承認を確認", "kgstudio.asset.tx-confirm-approval-modal.insufficient-balance-error.description": "管理者が十分な ${amount} $ {tokenSymbol}(${chainName}) を入金してから再試行してください", "kgstudio.asset.tx-confirm-approval-modal.insufficient-balance-error.title": "エラー:$ {tokenSymbol}(${chainName}) の在庫が不十分です", "kgstudio.asset.tx-confirm-approval-modal.send-token": "トークンを送信", "kgstudio.asset.tx-confirm-approval-modal.title": "承認を確認", "kgstudio.asset.tx-confirm-approval-modal.warning.description": "取引の詳細を注意深く確認し、その正当性を確認したことを確認してください。", "kgstudio.asset.tx-confirm-approval-modal.warning.title": "この取引を承認済みとしてマークしてもよろしいですか?", "kgstudio.asset.tx-confirm-release-modal.release": "リリースを確認", "kgstudio.asset.tx-confirm-release-modal.warning.description": "取引の詳細を注意深く確認し、その正当性を確認したことを確認してください。", "kgstudio.asset.tx-confirm-release-modal.warning.title": "このトランザクションをリリースしてもよろしいですか?", "kgstudio.asset.tx-conform-release-modal.title": "リリースを確認", "kgstudio.asset.tx-detail.not-exist": "この注文はこの組織には存在しません ({orgName})。代わりに、該当する組織を確認してください。", "kgstudio.asset.tx-detail.steps.awaiting-approval": "承認待ち", "kgstudio.asset.tx-detail.steps.awaiting-release": "リリース待ち", "kgstudio.asset.tx-detail.steps.rejected": "拒否", "kgstudio.asset.tx-detail.steps.send-failed": "送信失敗", "kgstudio.asset.tx-detail.steps.sending": "送信中", "kgstudio.asset.tx-detail.steps.send-success": "送信成功", "kgstudio.asset.tx-detail.steps.submitted": "送信済み", "kgstudio.asset.tx-detail.tx-info-card.tx-id": "トランザクション ID", "kgstudio.asset.tx-error-generic": "エラーが発生しました", "kgstudio.asset.tx-error-processing": "要求の処理中にエラーが発生しました。後でもう一度試してください。", "kgstudio.asset.tx-failed": "トランザクションの送信が失敗しました。もう一度試してください。 ", "kgstudio.asset.tx-history.approved": "{name}承認されました", "kgstudio.asset.tx-history.approved-by": "承認者", "kgstudio.asset.tx-history-card.approver": "承認者", "kgstudio.asset.tx-history-card.finance-manager": "ファイナンスマネージャー", "kgstudio.asset.tx-history-card.submitted-by": "提出者", "kgstudio.asset.tx-history-card.title": "歴史", "kgstudio.asset.tx-history-card.tx-hashes": "トランザクションハッシュ", "kgstudio.asset.tx-history.latest-update": "最新アップデート", "kgstudio.asset.tx-history.rejected": "{name}拒否", "kgstudio.asset.tx-history.rejected-by": "拒否者", "kgstudio.asset.tx-history.released": "{name}解放された", "kgstudio.asset.tx-history.released-by": "リリース元", "kgstudio.asset.tx-history.submitted": "{name}提出済み", "kgstudio.asset.tx-info-card.attachments": "添付ファイル", "kgstudio.asset.tx-info-card.blockchain": "ブロックチェーン", "kgstudio.asset.tx-info-card.recipient": "受信者", "kgstudio.asset.tx-info-card.send-token": "トークンを送信", "kgstudio.asset.tx-info-card.title": "トランザクション情報", "kgstudio.asset.tx-info-card.tx-note": "トランザクションノート", "kgstudio.asset.tx-info-card.tx-status": "トランザクションステータス", "kgstudio.asset.tx-insufficient-balance": "資産残高が不十分", "kgstudio.asset.tx-insufficient-balance-admin-recharge": "管理者に少なくとも {insufficientAmount}{tokenName}({chainName}) を財務省に預けてもらい、再試行してください", "kgstudio.asset.tx-limit-exceeded": "転送制限を超えました", "kgstudio.asset.tx-limit-exceeded-contact-admin": "転送制限を超えました。管理者に問い合わせてください", "kgstudio.asset.tx-need-approval": "この取引は承認後にのみ送信されます。", "kgstudio.asset.tx-need-approval-hint": "送信後は変更できないため、取引の詳細が正しいことを確認してください。", "kgstudio.asset.tx-rejection-modal.confirm-rejection": "拒否を確認", "kgstudio.asset.tx-rejection-modal.review-note.hint": "拒否理由", "kgstudio.asset.tx-rejection-modal.review-note.required-error": "拒否の理由を入力してください", "kgstudio.asset.tx-rejection-modal.review-note.title": "レビューメモ", "kgstudio.asset.tx-rejection-modal.success.title": "正常に拒否されました", "kgstudio.asset.tx-rejection-modal.title": "拒否を確認", "kgstudio.asset.tx-rejection-modal.warning.desc": "取引の詳細を注意深く確認し、拒否の理由を提供してください。", "kgstudio.asset.tx-rejection-modal.warning.title": "この取引を拒否してもよろしいですか？", "kgstudio.asset.understand": "分かりました", "kgstudio.asset.wallet-risk-check": "ウォレットリスクチェック", "kgstudio.audience.compliance": "コンプライアンス", "kgstudio.audience.country": "カントリー", "kgstudio.audience.email": "電子メール", "kgstudio.audience.kyc_status": "KYC", "kgstudio.audience.name": "[名前]", "kgstudio.audience.nft_projects": "NFT プロジェクト", "kgstudio.audience.phone": "電話", "kgstudio.audience.query-placeholder": "名前、電子メール、または電話番号", "kgstudio.audience.wallet_id": "ウォレット ID: ", "kgstudio.auth.login.accept-crypto-for": "の暗号を受け入れる", "kgstudio.auth.login.continue-with-email": "Eメールで続行", "kgstudio.auth.login.module.integration-options.description": "コード不要のソリューション、SDK統合、さまざまな技術的ニーズに対応する便利なAPIクエリを提供します。", "kgstudio.auth.login.module.integration-options.title": "フレキシブルなインテグレーション（ノーコード、SDK、API）", "kgstudio.auth.login.module.low-fees.description": "手数料が最小限に抑えられているため、クリエイターは収益をより多く維持できます。", "kgstudio.auth.login.module.low-fees.title": "低料金", "kgstudio.auth.login.module.no-kyc.description": "KYC認証や事業者登録は必要ありません。すぐに販売を開始してください。", "kgstudio.auth.login.module.no-kyc.title": "制限なし", "kgstudio.auth.login.module.payment-options.description": "クレジットカードまたはダイレクトウォレット決済によるチャージをサポートし、さまざまな取引ニーズに対応します。", "kgstudio.auth.login.module.payment-options.title": "複数の支払いオプション", "kgstudio.auth.login.rotator.artwork": "🎨 アートワーク", "kgstudio.auth.login.rotator.business": "💼 ビジネス", "kgstudio.auth.login.rotator.community": "👥 コミュニティ", "kgstudio.auth.login.rotator.content": "📝 コンテンツ作成", "kgstudio.auth.login.rotator.digital-goods": "🎁 デジタル商品", "kgstudio.auth.login.rotator.website": "💻 ウェブサイト", "kgstudio.check.change_acc_apologize": "申し訳ありませんが、このチームに参加することはできません。", "kgstudio.check.change_acc_desc": "現在のログインメールは{currentEmail}です。代わりに招待メール{inviteEmail}を使用してログインしてください。", "kgstudio.check.change_acc_link": "ログアウト", "kgstudio.check.change_acc_title": "チームに参加するためにログインアカウントを変更してください。", "kgstudio.check.change_acc_toast": "現在のアカウントからログアウトしました。新しいアカウントにリダイレクトされます。", "kgstudio.check.invalid_desc": "URLを確認するか、組織の管理者に連絡してください。", "kgstudio.check.invalid_link": "ホームページに戻る", "kgstudio.check.invalid_title": "申し訳ありませんが、このリンクは期限切れかページが見つかりません", "kgstudio.check.invitation-accepted-cta": "ログイン", "kgstudio.check.invitation-accepted-desc": "ログインして旅を始めましょう", "kgstudio.check.invitation-accepted-title": "おめでとう。登録が完了しました。", "kgstudio.check.loading_desc": "認証を確認しています...", "kgstudio.check.loading_title": "お待ちください、このページを離れたり閉じたりしないでください。", "kgstudio.common.accept": "承諾する", "kgstudio.common.account_setting": "アカウント設定", "kgstudio.common.add-fund": "ファンドを追加", "kgstudio.common.address": "住所", "kgstudio.common.address-copied": "アドレスがクリップボードにコピーされました", "kgstudio.common.all-tasks": "タスク詳細", "kgstudio.common.app-notification": "アプリ通知", "kgstudio.common.app-publish": "アプリ公開", "kgstudio.common.approved": "承認済み", "kgstudio.common.asset": "AssetPro", "kgstudio.common.assets": "資産", "kgstudio.common.attachments": "添付ファイル", "kgstudio.common.back": "戻る", "kgstudio.common.back-to-login": "ログインに戻る", "kgstudio.common.billing": "請求", "kgstudio.common.blockchain": "ブロックチェーン", "kgstudio.common.cancel": "[キャンセル]", "kgstudio.common.case-management": "ケースマネジメント", "kgstudio.common.cdd-tasks": "CDD タスク", "kgstudio.common.change": "[変更]", "kgstudio.common.clear-filter": "フィルターをクリア", "kgstudio.common.close": "閉じる", "kgstudio.common.community-links": "コミュニティリンク", "kgstudio.common.complete": "完了", "kgstudio.common.compliance": "Compliance", "kgstudio.common.configuration": "ウォレット設定", "kgstudio.common.confirm-publish": "公開を確認", "kgstudio.common.create": "作成", "kgstudio.common.create-a-task": "タスクを作成", "kgstudio.common.created": "作成日時", "kgstudio.common.created_at": "作成日時", "kgstudio.common.dapp-list": "DAppリスト", "kgstudio.common.data.analysis": "[データ]", "kgstudio.common.data.asset-pro": "AssetPro", "kgstudio.common.data.compliance": "Compliance", "kgstudio.common.data.nft-boost": "NFT アクティビティ", "kgstudio.common.data.wallet": "財布", "kgstudio.common.description": "[説明]", "kgstudio.common.discord": "Discord", "kgstudio.common.edit": "[編集]", "kgstudio.common.editor": "エディター", "kgstudio.common.engage": "マーケティングツール (coming soon)", "kgstudio.common.error": "何かがおかしくなった。後でもう一度試すか、サポートチームにお問い合わせください。", "kgstudio.common.expired": "期限切れ", "kgstudio.common.explorer-banner": "エクスプローラーバナー", "kgstudio.common.export-private-key": "プライベートキーをエクスポート", "kgstudio.common.finance": "金融", "kgstudio.common.general": "一般", "kgstudio.common.get-started": "始めましょう", "kgstudio.common.idv-tasks": "IDV タスク", "kgstudio.common.image": "[イメージ]", "kgstudio.common.in-app-message": "アプリ内メッセージ", "kgstudio.common.insufficient_not_refunded": "不十分 (返金なし)", "kgstudio.common.insufficient_refunded": "不十分 (返金済み)", "kgstudio.common.kyc-form": "本人確認フォーム", "kgstudio.common.kyt-tasks": "KYA タスク", "kgstudio.common.language.english": "English", "kgstudio.common.language.japanese": "日本語", "kgstudio.common.languages": "言語", "kgstudio.common.language.simplified-chinese": "中文（简体）", "kgstudio.common.language.spanish": "Español", "kgstudio.common.language.traditional-chinese": "中文（繁體）", "kgstudio.common.language.vietnamese": "Tiếng <PERSON>", "kgstudio.common.last-edited-time": "最終編集日時", "kgstudio.common.line-id": "LINE ID", "kgstudio.common.liquidity": "流動性", "kgstudio.common.logout": "ログアウト", "kgstudio.common.marketing-tools": "マーケティングツール", "kgstudio.common.market-settings": "マーケット設定", "kgstudio.common.maxInputHint": "最高 1000 字", "kgstudio.common.member-id": "メンバー ID", "kgstudio.common.members": "チームメンバー", "kgstudio.common.minimum": "最小", "kgstudio.common.my-role": "私の役割", "kgstudio.common.my-shop": "マイショップ", "kgstudio.common.next": "[次へ]", "kgstudio.common.next-step": "次のステップ", "kgstudio.common.nft-airdrop": "NFT エアドロップ", "kgstudio.common.nft-boost": "NFT ブースト", "kgstudio.common.note-and-attachments": "メモと添付ファイル", "kgstudio.common.notification": "通知", "kgstudio.common.operators": "オペレータ", "kgstudio.common.optional": "[オプション]", "kgstudio.common.orders": "注文", "kgstudio.common.organization-id-required": "組織 ID が必要です", "kgstudio.common.overview": "概要", "kgstudio.common.page-desc": "1 ページあたりの行を表示", "kgstudio.common.payment-address": "支払い住所", "kgstudio.common.pending": "保留中", "kgstudio.common.prev-step": "前のステップ", "kgstudio.common.processing": "読み込み中...", "kgstudio.common.products": "[製品]", "kgstudio.common.profile": "プロフィール", "kgstudio.common.project": "[プロジェクト]", "kgstudio.common.project-updated": "プロジェクト情報を更新しました！", "kgstudio.common.push-notification": "プッシュ通知", "kgstudio.common.recaptcha-error": "Google 再キャプチャの検証に失敗しました。ウェブページを更新するか、サポートチームにお問い合わせください。", "kgstudio.common.recipient": "受信者", "kgstudio.common.reject": "拒否", "kgstudio.common.rejected": "拒否", "kgstudio.common.reset": "リセット", "kgstudio.common.revenue": "金融", "kgstudio.common.revert": "拒否してキャンセル", "kgstudio.common.review": "レビューセンター", "kgstudio.common.roles": "役割", "kgstudio.common.save": "[保存]", "kgstudio.common.save-changes": "変更を保存しました！", "kgstudio.common.save-draft": "下書きを保存", "kgstudio.common.search": "検索", "kgstudio.common.send": "[送信]", "kgstudio.common.send-now": "今すぐ送信", "kgstudio.common.send-to": "送信先", "kgstudio.common.send-token": "トークンを送信", "kgstudio.common.settings": "[設定]", "kgstudio.common.start": "[開始]", "kgstudio.common.submit": "送信", "kgstudio.common.submit-request": "送信", "kgstudio.common.success": "成功", "kgstudio.common.successfully-copied": "正常にコピーされました！", "kgstudio.common.supported-chains": "対応ブロックチェーン", "kgstudio.common.system-settings": "システム設定", "kgstudio.common.telegram": "Telegram", "kgstudio.common.top-up": "トップアップ", "kgstudio.common.total": "合計", "kgstudio.common.transaction-status": "トランザクションステータス", "kgstudio.common.transfer": "転送", "kgstudio.common.treasury": "財務省", "kgstudio.common.twitter": "Twitter", "kgstudio.common.tx-hash": "トランザクションハッシュ", "kgstudio.common.type": "タイプ", "kgstudio.common.type-some-description": "ここに説明を入力してください。", "kgstudio.common.unset": "設定解除", "kgstudio.common.update": "[更新]", "kgstudio.common.updated_at": "更新日時", "kgstudio.common.update-time": "更新時間", "kgstudio.common.uploading": "アップロード中...", "kgstudio.common.user": "[ユーザー設定]", "kgstudio.common.user360": "User 360", "kgstudio.common.user-management": "すべてのユーザ", "kgstudio.common.user-not-found": "Studio ユーザーが見つかりません。組織の管理者に問い合わせてください。", "kgstudio.common.users": "ユーザ", "kgstudio.common.wallet": "ウォレットビルダー", "kgstudio.compliance.cdd-tasks": "CDD タスク", "kgstudio.compliance.idv-tasks": "IDV タスク", "kgstudio.compliance.kyt-tasks": "KYA タスク", "kgstudio.compliance.title": "Compliance", "kgstudio.data.actions-section": "アクション", "kgstudio.data.active-users": "アクティブユーザ", "kgstudio.data.ai-generate": "AIジェネレーター！", "kgstudio.data.ai-recommend": "AIレコメンド", "kgstudio.data.all": "[すべて]", "kgstudio.data.app-store-info-language": "アプリストア情報言語", "kgstudio.data.asset-pro.date.all": "[すべて]", "kgstudio.data.asset-pro.date.last-14-days": "過去 14 日間", "kgstudio.data.asset-pro.date.last-30-days": "過去 30 日間", "kgstudio.data.asset-pro.date.last-7-days": "過去 7 日間", "kgstudio.data.asset-pro.date.title": "日付", "kgstudio.data.asset-pro.error": "データを生成できませんでした。後でもう一度試してください。", "kgstudio.data.asset-pro.loading": "AssetPro データ用のダッシュボードを生成中...", "kgstudio.data.asset-pro.retry": "リトライ", "kgstudio.data.balance": "バランス", "kgstudio.data.balance-greater-than-zero": "バランス > 0", "kgstudio.data.budget-title": "予算", "kgstudio.data.choose-plan": "プランを選択", "kgstudio.data.churn-rate": "解約率", "kgstudio.data.churn-users": "ユーザー解体", "kgstudio.data.compliance.cdd-tasks": "ケースレビューの数", "kgstudio.data.compliance.form-submission": "フォーム送信", "kgstudio.data.compliance.idv-tasks": "本人確認の件数", "kgstudio.data.compliance.kyc-status.title": "本人確認書認証", "kgstudio.data.compliance.no-data": "分析できるデータはまだありません", "kgstudio.data.compliance.pending-review": "審査待ち", "kgstudio.data.compliance.personal-info.title": "個人情報", "kgstudio.data.compliance.verified-customers": "認証済みのお客様", "kgstudio.data.compliance.v-wallet-usage.title": "認証済み顧客のウォレットアクティベーション", "kgstudio.data.compliance.v-wallet-usage.tooltip": "KYC認証済みユーザーがKryptoGoウォレットをアクティベート（ダウンロードしてログイン）したかどうか。", "kgstudio.data.create_nft_collection": "NFT コレクションを作成する", "kgstudio.data.discard": "捨てる", "kgstudio.data.engage.compliance": "コンプライアンス", "kgstudio.data.engage.country": "カントリー", "kgstudio.data.engage-create": "エンゲージメントを作成", "kgstudio.data.engage.email": "電子メール", "kgstudio.data.engage.kyc": "KYC", "kgstudio.data.engage-list": "エンゲージメントプロジェクトリスト", "kgstudio.data.engage.name": "[名前]", "kgstudio.data.engage.nft-projects": "NFT プロジェクト", "kgstudio.data.engage.phone": "電話", "kgstudio.data.engage-title": "従事する", "kgstudio.data.engage.wallet-id": "ウォレット ID", "kgstudio.data.estimated-gas-fee": "推定ガス料金", "kgstudio.data.estimated-reach": "推定リーチ", "kgstudio.data.events": "イベント", "kgstudio.data.evm-wallet": "EVM ウォレット", "kgstudio.data.file_types_supported": "サポートされている画像: JPG、PNG、GIF、SVG 最大サイズ: 10 MB (ファイル形式の名前を変更しても機能しません。対応している画像形式がない場合は、ファイル変換サービスをご利用ください。)", "kgstudio.data.header": "ウォレットデータ", "kgstudio.data.increase-user-retention": "ユーザーリテンションの向上", "kgstudio.data.increase-wallet-user-activity": "登録ユーザーアクティベーション", "kgstudio.data.increase-wallet-user-retention": "ウォレットユーザーリテンション", "kgstudio.data.invalid": "情報が正しく入力されているか確認してください", "kgstudio.data.login": "ログイン", "kgstudio.data.new-users": "新規ユーザ", "kgstudio.data.nft_opensea_banner_title": "NFT オープンシーバナー", "kgstudio.data.notification.1-button": "1 ボタン", "kgstudio.data.notification.2-buttons": "2 ボタン", "kgstudio.data.notification.body": "ボディ", "kgstudio.data.notification.buttons": "ボタン", "kgstudio.data.notification.enter-title": "タイトルを入力", "kgstudio.data.notification.file-types-supported": "サポートされている画像: JPG、PNG、GIF、SVG 最大サイズ: 10 MB (ファイル形式の名前を変更しても機能しません。対応している画像形式がない場合は、ファイル変換サービスをご利用ください。)", "kgstudio.data.notification.first-button": "最初のボタン", "kgstudio.data.notification.image": "画像", "kgstudio.data.notification.no-button": "[ボタンなし]", "kgstudio.data.notification.recommended-size": "推奨サイズ:1400 x 350 ピクセル", "kgstudio.data.notification.text-placeholder": "[テキスト]", "kgstudio.data.recommended_size": "推奨サイズ:600 x 200 ピクセル", "kgstudio.data.recommended-target": "推奨ターゲット", "kgstudio.data.registered-in-7D": "7 日間で登録済み", "kgstudio.data.registered-users": "登録ユーザ", "kgstudio.data.retention": "リテンション", "kgstudio.data.retention-rate": "リテンション率", "kgstudio.data.select_from_nft_boost": "NFTブーストから選択", "kgstudio.data.select-multiple-actions": "ユーザーには複数の種類の資産/情報を設定できます。", "kgstudio.data.send-currency": "通貨を送信", "kgstudio.data.send-nft": "NFTを送信", "kgstudio.data.send-notification": "通知を送信", "kgstudio.data.shop-info-language": "店舗情報言語版", "kgstudio.data.since-last-month": "先月から", "kgstudio.data.specify-engage-time": "スケジュール", "kgstudio.data.target-placeholder": "目標 (例:リテンション率の向上)", "kgstudio.data.target-section": "ターゲット", "kgstudio.data.target-title": "ゴール", "kgstudio.data.time-option-custom": "カスタム", "kgstudio.data.time-option-immediately": "直ちに", "kgstudio.data.time-section": "時間", "kgstudio.data.top-users": "トップユーザ", "kgstudio.data.total-balance": "合計残高", "kgstudio.data.tron-wallet": "トロンウォレット", "kgstudio.data.try-more": "もっと試してみる", "kgstudio.data.tx-events": "トランザクションイベント", "kgstudio.data.upload-branding-assets": "ブランドアセットのアップロード", "kgstudio.data.upload-csv-list": ".csv リストをアップロード", "kgstudio.data.use-assetpro": "AssetProを使用して、ターゲットユーザーに暗号通貨を送信します", "kgstudio.data.use-nft-boost": "NFT Boostの既存のNFTアイテムから選択", "kgstudio.data.user": "ユーザ", "kgstudio.data.user-activities": "ユーザーアクティビティ", "kgstudio.data.users-balance-greater": "ユーザーバランス > 0", "kgstudio.data.use-wallet-notification": "ウォレットユーザーに通知とお知らせを送信", "kgstudio.data.wallet": "財布", "kgstudio.data.wallet-address": "ウォレットアドレス", "kgstudio.data.wallet-balance-greater": "ウォレット残高 > 0", "kgstudio.data.wallets": "ウォレット", "kgstudio.data.your-prompt": "あなたのプロンプト", "kgstudio.dna.create-at": "作成日", "kgstudio.dna.token-holdings": "トークン・ホールディングス", "kgstudio.engagement.actions-title": "アクション", "kgstudio.engagement.activity": "アクティビティ", "kgstudio.engagement.all": "[すべて]", "kgstudio.engagement.asset-balance": "資産残高", "kgstudio.engagement.behavior": "動作", "kgstudio.engagement.engaged": "従事した", "kgstudio.engagement.methods": "メソッド", "kgstudio.engagement.name": "[プロジェクト名]", "kgstudio.engagement.nft-claim-rate": "NFT クレームレート", "kgstudio.engagement.notification-visit": "通知訪問", "kgstudio.engagement.rewards-redeem-rate": "リワード交換レート", "kgstudio.engagement.send-currency": "通貨を送信", "kgstudio.engagement.send-nft": "NFTを送信", "kgstudio.engagement.send-notification": "通知を送信", "kgstudio.engagement.target-settings": "ターゲット設定", "kgstudio.engagement.time": "時間", "kgstudio.engagement.title": "エンゲージメント", "kgstudio.engagement.users": "ユーザ", "kgstudio.error.cant-find-customer": "申し訳ありませんが、このユーザーはまだサービスに登録していません。このユーザーへの送金をご希望の場合は、まずKYCフォームまたはウォレットに登録してログインするように依頼してください。", "kgstudio.error.cant-find-user": "申し訳ございません、このユーザーを見つけることができません。", "kgstudio.error.general-error": "何らかのエラーが発生しました。コード: ", "kgstudio.error.insufficient-balance": "残高が不十分です。組織のトレジャリープールにさらに資金を追加してください。", "kgstudio.error.no-access": "アクセス権がありません。認証済みのウォレットアドレスを使用してログインしてください。", "kgstudio.error.no-organization": "申し訳ありませんが、まだどの組織にも参加していません", "kgstudio.error.no-organization-contact": "組織の管理者に連絡するか、KryptoGo に連絡してビジネスプランを開始してください。", "kgstudio.error.out-of-range": "入力値が範囲外です。", "kgstudio.error.permission-denied.desc": "このページにアクセスする権限がありません。", "kgstudio.error.permission-denied.title": "許可が拒否されました。", "kgstudio.error.please-try-again": "後でもう一度試してください", "kgstudio.error.resource-not-found.desc": "お探しのページは存在しません。", "kgstudio.error.resource-not-found.title": "リソースが見つかりません。", "kgstudio.error.something-went-wrong": "申し訳ありませんが、問題が発生しました", "kgstudio.error.try-again": "エラーが発生しました。後でもう一度試してください。", "kgstudio.error.upload-attachment": "添付ファイルをアップロードできませんでした", "kgstudio.error.upload-image-failed": "画像をアップロードできませんでした", "kgstudio.error.upload-max-files": "最大ファイル数を超えました。", "kgstudio.error.upload-max-file-size": "ファイルサイズが 10 MB を超えています。", "kgstudio.error.upload-unsupported-file": "サポートされていないファイル形式です。", "kgstudio.error.user-not-found": "ユーザーが見つかりません。", "kgstudio.home.awaiting-approval-tx.title": "承認待ち取引", "kgstudio.home.awaiting-release-tx.title": "リリーストランザクション待ち", "kgstudio.home.duplicate-org-name": "組織名は既に存在します", "kgstudio.home.edit-organization.icon-hint1": "サポートされているフォーマット:JPG、PNG、ウェブピー、SVG", "kgstudio.home.edit-organization.icon-hint2": "推奨サイズ:100 x 100 ピクセル", "kgstudio.home.edit-organization.icon-hint3": "最大 10 メガバイト", "kgstudio.home.edit-organization.icon-title": "アイコン", "kgstudio.home.edit-organization.name-error1": "名前は2～20文字でなければなりません", "kgstudio.home.edit-organization.name-error2": "名前に無効な文字 (@、#、$、/、*、...) が含まれています", "kgstudio.home.edit-organization.name-placeholder": "組織", "kgstudio.home.edit-organization.name-title": "[名前]", "kgstudio.home.edit-organization.success": "組織の設定が更新されました！", "kgstudio.home.edit-organization-title": "組織を編集", "kgstudio.home.joyride.step1": "アカウント設定ページでは、AIエージェントを使用して暗号支払いページを作成したり、開発者に適した支払い機能を既存のアプリケーションに統合したりできます。", "kgstudio.home.joyride.step2": "製品リストページでは、独自の製品を作成し、1ページの支払いリンクを取得して誰とでも共有できます。", "kgstudio.home.joyride.step3": "トレジャリーページでは、ウォレットの現在の収益を確認したり、トレジャリープールを管理したりできます。", "kgstudio.home.joyride.step4": "こちらのボタンをクリックして、上記のページにアクセスすることもできます。", "kgstudio.home.joyride.step5": "まだ商品はありませんか？ここをクリックして最初の製品を作成してください。", "kgstudio.home.kyc-pending.title": "KYC 審査保留中", "kgstudio.home.kyc-pending.tooltip": "まだ審査されていないKYC申請の数", "kgstudio.home.my-pending-tx.title": "私の保留中の取引", "kgstudio.home.orders-pending.title": "保留中の注文", "kgstudio.home.orders-pending.tooltip": "まだ処理されていない注文の数", "kgstudio.home.payment.api-key-settings": "API キー設定", "kgstudio.home.payment.create-product": "製品を作成", "kgstudio.home.payment.data": "支払いデータ", "kgstudio.home.payment.manage-wallet": "ウォレットを管理", "kgstudio.home.payment.total-order": "注文総額", "kgstudio.home.payment.total-revenue": "総収入", "kgstudio.home.payment.unique-customer": "ユニークカスタマー", "kgstudio.image.upload-required": "画像をアップロードしてください", "kgstudio.kyc-status.pending": "保留中", "kgstudio.kyc-status.rejected": "拒否", "kgstudio.kyc-status.transfer-hint.pending": "このユーザーのKYCレビューはまだ処理中です。できるだけ早くレビューを完了させ、注意して取引してください。", "kgstudio.kyc-status.transfer-hint.rejected": "このユーザーはKYCレビューに失敗しました、注意して取引してください。", "kgstudio.kyc-status.transfer-hint.unverified": "このユーザーはまだKYCの確認を受けていません、注意して取引してください。", "kgstudio.kyc-status.transfer-hint.verified": "このユーザーは KYC の確認を受けています！", "kgstudio.kyc-status.unverified": "未確認", "kgstudio.kyc-status.verified": "確認済み", "kgstudio.login.with-google": "", "kgstudio.message.input-message-content": "メッセージの内容を入力してください", "kgstudio.nft.airdrop": "エアドロップ", "kgstudio.nft.back-to-list": "プロジェクトリストに戻る", "kgstudio.nft.balance-and-fee": "{balance}Polygonウォレットの残高はPOLです。{createCollectionFee}このプロジェクトにはほぼPOLが必要です。", "kgstudio.nft.campaign": "キャンペーン", "kgstudio.nft.claimed": "主張した", "kgstudio.nft.claimed-total": "請求件数/合計件数", "kgstudio.nft.collection-name-hint": "40 文字以内の英数字を入力してください。", "kgstudio.nft.collection-symbol-hint": "10 文字以内 (通常はコレクション名の略語) を推奨", "kgstudio.nft.confirm-publish": "公開を確認", "kgstudio.nft.create-collection-fee": "NFTコレクションを作成するには、{createCollectionFee}ほぼPOLが必要です。", "kgstudio.nft.delivery-method-title": "配送方法", "kgstudio.nft.edit-button-text": "[編集]", "kgstudio.nft.end-date-title": "終了日", "kgstudio.nft.error.collection-name-existed": "コレクション名は既に存在します", "kgstudio.nft.error.project-not-found": "プロジェクトが見つかりません", "kgstudio.nft.favicon-title": "ファビコン", "kgstudio.nft.form.banner-file-types": "サポートされている画像: JPG、PNG、GIF、SVG 最大サイズ: 100 メガバイト。(ファイル形式の名前を変更しても機能しません。対応している画像形式がない場合は、ファイル変換サービスをご利用ください。)", "kgstudio.nft.form.banner-recommended-size": "推奨サイズ:1400 x 350 ピクセル。", "kgstudio.nft.form.collection-description": "NFTの説明", "kgstudio.nft.form.collection-description-hint": "1000 文字まで入力できます。", "kgstudio.nft.form.collection-name": "NFT コレクション名", "kgstudio.nft.form.collection-name-hint": "最大 40 文字の英数字テキストを入力してください。", "kgstudio.nft.form.contract-schema-name": "コントラクトスキーマ名", "kgstudio.nft.form.favicon": "ファビコン画像", "kgstudio.nft.form.favicon-image-title": "ファビコン画像", "kgstudio.nft.form.max-supply": "マックスサプライ", "kgstudio.nft.form.max-supply-hint": "NFTの発行には、Gas Fee: {mintFee} POL（現在のネットワークアクティビティによって異なります）が必要になる場合があります。", "kgstudio.nft.form.max-supply-label": "NFT 総供給量 (最大供給量)", "kgstudio.nft.form.mint-time-customized": "カスタムタイム", "kgstudio.nft.form.mint-time-end": "受信終了", "kgstudio.nft.form.mint-time-end-hint": "2038/01/01 00:00 (UTC) より後の時間を設定することはできません。", "kgstudio.nft.form.mint-time-end-instant": "なし (2038年1月1日に終了)", "kgstudio.nft.form.mint-time-instant": "未設定 (NFTはリリース後すぐに受け取れます)", "kgstudio.nft.form.mint-time-start": "受信開始", "kgstudio.nft.form.nft-image": "NFT イメージ", "kgstudio.nft.form.nft-opensea-banner": "NFT オープンシーバナー", "kgstudio.nft.form.placeholder.description": "商品の詳細な説明を記入してください。", "kgstudio.nft.form.received-method": "受信方法", "kgstudio.nft.form.received-method-address": "リンクされたウォレットアドレス", "kgstudio.nft.form.received-method-email": "Eメールを入力", "kgstudio.nft.form.received-method-phone": "電話番号を入力", "kgstudio.nft.form.subtitle": "字幕", "kgstudio.nft.form.symbol-name": "NFT シンボル名", "kgstudio.nft.form.symbol-name-hint": "最大文字数:10 文字。英字のみ。スペースを入れることはできません。多くの場合、KGYC などのコレクション名に基づいています。", "kgstudio.nft.form.title": "ページタイトル", "kgstudio.nft.form.upload-ico-desc": ".ico ファイルをアップロードしてください", "kgstudio.nft.form.upload-icon-file": ".ico ファイルをアップロードしてください", "kgstudio.nft.free-claim": "フリークレームNFT", "kgstudio.nft.go-to-project": "詳細を表示", "kgstudio.nft.image-collection-item": "NFT コレクション画像、NFT アイテム画像", "kgstudio.nft.image-file-types": "サポートされているフォーマット: JPG、PNG、GIF、SVG 最大サイズ: 10 MB  (ファイル形式の名前を変更しても機能しません。対応している画像形式がない場合は、ファイル変換サービスをご利用ください。)", "kgstudio.nft.image-recommended-size": "推奨サイズ:2000 x 2000 ピクセル", "kgstudio.nft.info.contract": "契約住所", "kgstudio.nft.info.creator": "クリエーター", "kgstudio.nft.info.na": "該当なし", "kgstudio.nft.insufficient-balance": "バランスが不十分！", "kgstudio.nft.label.success-sms-preview": "成功 SMS プレビュー", "kgstudio.nft.mint-fee": "NFTをマイニングするための実際のガス料金は、{mintFee}ほぼPOLです（現在のネットワークアクティビティによって異なります）。", "kgstudio.nft.mint-page.na": "アクティビティリンクはまだありません。", "kgstudio.nft.mint-page-name": "NFTキャンペーンサイト", "kgstudio.nft.mint-page.pending": "キャンペーンサイトを作成中... キャンペーンリンクはまもなく利用可能になります！", "kgstudio.nft.mint-page-title": "NFT ミントページ", "kgstudio.nft.mint-time": "ミントタイム", "kgstudio.nft.modal.edit-mint-page": "NFT ミントページの編集", "kgstudio.nft.next-step": "次のステップ", "kgstudio.nft.nft-collection": "NFT コレクション", "kgstudio.nft.nft-collection-chain": "NFTはポリゴンチェーンで発行されます。", "kgstudio.nft.nft-projects": "NFT プロジェクト", "kgstudio.nft.notification.draft": "ドラフトステータス:あなたのNFTはまだ公開されていません！", "kgstudio.nft.notification.failed": "NFTの公開中にエラーが発生しました！", "kgstudio.nft.notification.pending": "NFTコントラクトの展開が進行中です。後でこのページをもう一度確認してください。", "kgstudio.nft.notification.published": "NFTは正常に公開されました！", "kgstudio.nft.overview": "[概要]", "kgstudio.nft.placeholder.success-sms": "商品の詳細な説明を記入してください。", "kgstudio.nft.preview.collection": "NFT コレクションプレビュー", "kgstudio.nft.preview.mint-page": "プレビュー - NFTキャンペーンサイト", "kgstudio.nft.prev-step": "前のステップ", "kgstudio.nft.processing": "処理中です。しばらくお待ちください", "kgstudio.nft.processing-description": "NFT コントラクトがデプロイされています。これには数分かかる場合があります。NFTが公開されるのを待っている間は、NFTプロジェクトページに移動して最新情報を入手するか、NFTプロジェクトリストに戻って閲覧を続けることができます。", "kgstudio.nft.qr-code-title": "QR コード", "kgstudio.nft.recharge-balance": "{balanceDifference}NFTプロジェクトを公開するには、POLをウォレットにリチャージする必要があります。", "kgstudio.nft.reward": "報酬", "kgstudio.nft.save-draft": "下書きを保存", "kgstudio.nft.saved-successfully-toast": "NFT プロジェクト {collectionName} のドラフトが保存されました！", "kgstudio.nft.scan-to-visit": "スキャンして訪問", "kgstudio.nft.start-date-title": "開始日", "kgstudio.nft.status.claimed": "請求済み", "kgstudio.nft.status.claim-rate": "クレームレート", "kgstudio.nft.status.draft": "ドラフト", "kgstudio.nft.status.failed": "失敗", "kgstudio.nft.status.pending": "保留中", "kgstudio.nft.status.published": "公開済み", "kgstudio.nft.status.total": "合計", "kgstudio.nft.step.check": "プレビューと確認", "kgstudio.nft.step.collection": "NFTコレクションの基本情報を入力してください", "kgstudio.nft.step.mint": "NFT 配布イベントの設定", "kgstudio.nft.subtitle-title": "字幕", "kgstudio.nft.success-sms-title": "成功 SMS 通知", "kgstudio.nft.text.success-sms": "【NFT受信通知】{collectionName}  NFTの受け取りへようこそ！KryptoGo ウォレットアプリを開いて、電話番号を使ってログインしてください。{appLink}(アプリダウンロードリンク:)", "kgstudio.nft.title-title": "タイトル", "kgstudio.nft.total": "合計", "kgstudio.nft.validation.collection-desc": "NFT コレクションの説明を入力してください", "kgstudio.nft.validation.collection-name": "NFT コレクション名を入力してください", "kgstudio.nft.validation.collection-name-max": "NFT コレクション名は 40 文字を超えることはできません", "kgstudio.nft.validation.collection-name-min": "NFT コレクション名を入力してください", "kgstudio.nft.validation.enter-collection-abbreviation": "NFT コレクションの略語を入力してください", "kgstudio.nft.validation.enter-total-supply": "NFT コレクションの総供給量を入力してください", "kgstudio.nft.validation.only-english-and-whitespace": "英語の文字と空白のみを使用できます", "kgstudio.nft.wallet-balance": "ポリゴンウォレットの残高は", "kgstudio.nft.wallet-balance-matic": "POL。残高がNFTの作成と発行にかかるガス料金を賄うのに十分であることを確認してください。", "kgstudio.onboarding.category-arts": "", "kgstudio.onboarding.category-arts-advice": "", "kgstudio.onboarding.category-arts-description": "", "kgstudio.onboarding.category-arts-name": "", "kgstudio.onboarding.category-educational": "", "kgstudio.onboarding.category-educational-advice": "", "kgstudio.onboarding.category-educational-description": "", "kgstudio.onboarding.category-educational-name": "", "kgstudio.onboarding.category-other": "", "kgstudio.onboarding.category-products": "", "kgstudio.onboarding.category-products-advice": "", "kgstudio.onboarding.category-products-description": "", "kgstudio.onboarding.category-products-name": "", "kgstudio.onboarding.check-store": "", "kgstudio.onboarding.close": "", "kgstudio.onboarding.copy-link": "", "kgstudio.onboarding.create-error": "", "kgstudio.onboarding.currency": "", "kgstudio.onboarding.fill-required-fields": "", "kgstudio.onboarding.go": "", "kgstudio.onboarding.image": "", "kgstudio.onboarding.next": "", "kgstudio.onboarding.prev": "", "kgstudio.onboarding.product-description": "", "kgstudio.onboarding.product-name": "", "kgstudio.onboarding.product-price": "", "kgstudio.onboarding.receive-address": "", "kgstudio.onboarding.revenue-over-3000": "", "kgstudio.onboarding.revenue-under-3000": "", "kgstudio.onboarding.skip": "", "kgstudio.onboarding.step1.title": "", "kgstudio.onboarding.step2.title": "", "kgstudio.onboarding.step3.title": "", "kgstudio.onboarding.step4.title": "", "kgstudio.onboarding.step6.title": "", "kgstudio.onboarding.step7.title": "", "kgstudio.onboarding.step8.description": "", "kgstudio.onboarding.step8.title": "", "kgstudio.onboarding.supported-chains": "", "kgstudio.operators.approval-threshold-desc": "指定された金額に達すると、振込の承認が必要です。", "kgstudio.operators.daily-transfer-limit": "1日の送金限度額", "kgstudio.operators.edit-operator": "編集オペレータ", "kgstudio.operators.page-title": "オペレータ", "kgstudio.operators.placeholder": "名前、電子メール、IDを検索", "kgstudio.operators.threshold-amount": "しきい値", "kgstudio.operators.title": "アセットプロオペレーター", "kgstudio.operators.transfer-approval-threshold": "譲渡承認基準額", "kgstudio.operators.transfer-approval-threshold-desc": "このしきい値を超えるトランザクションは、送信前に承認が必要です。", "kgstudio.operators.transfer-limit-desc": "オペレーターがAssetProで送金できる1日の最大金額で、毎日 00:00 にリセットされます。", "kgstudio.operators.transfer-limit-error": "しきい値は1日の転送限度を超えることはできません", "kgstudio.organization.create.back": "戻る", "kgstudio.organization.create.back-to-login": "ログインに戻る", "kgstudio.organization.create.button": "組織を作成", "kgstudio.organization.create.email-description": "このメールは、組織内のアカウントに関連付けられます。", "kgstudio.organization.create.email-placeholder": "Eメールアドレスを入力してください", "kgstudio.organization.create.error.failed": "組織を作成できませんでした。もう一度試してください。", "kgstudio.organization.create.error.login-failed": "組織作成後にログインできませんでした。もう一度試してください。", "kgstudio.organization.create.error.missing-token": "認証トークンが見つかりません。ログインをやり直してください。", "kgstudio.organization.create.login-success": "正常にログインしました！", "kgstudio.organization.create.org-name-placeholder": "組織名を入力", "kgstudio.organization.create.subtitle.existing-user": "新しい組織を作成", "kgstudio.organization.create.subtitle.new-user": "組織を作成して始めましょう", "kgstudio.organization.create.success": "組織は正常に作成されました", "kgstudio.overview.applications": "アプリケーション", "kgstudio.overview.assetpro-intro": "軽量で安全な暗号キャッシュフロー管理システム。", "kgstudio.overview.compliance-intro": "規制の変化に適応できるグローバルなコンプライアンス。", "kgstudio.overview.create-date": "作成日", "kgstudio.overview.nft-intro": "コードなしで、NFTキャンペーンを簡単に作成できます。", "kgstudio.overview.no-access": "このモジュールにアクセスする権限がないようです。このモジュールを使用する場合は、システム所有者に連絡して有効化の手続きを依頼してください。", "kgstudio.overview.user360-intro": "あなたの Web3 コマンドセンター。", "kgstudio.overview.wallet-intro": "ユーザー中心のブランド限定のウォレット。", "kgstudio.page.input-page-subtitle": "ページのサブタイトルを入力してください", "kgstudio.page.input-page-title": "ページタイトルを入力してください", "kgstudio.page.page-size-description": "{pageSize}1 ページあたりの行数を表示", "kgstudio.payment.accent-color": "メインカラー", "kgstudio.payment.add-field": "[フィールドを追加]", "kgstudio.payment.aggregated-amount": "集計金額", "kgstudio.payment.all-clients": "すべてのクライアント", "kgstudio.payment.amount": "金額", "kgstudio.payment.button-preview": "ボタンプレビュー", "kgstudio.payment.callback-dashboard": "コールバックダッシュボード", "kgstudio.payment.callback-details": "コールバック詳細", "kgstudio.payment.callback-id": "コールバック ID", "kgstudio.payment.callback-payload": "コールバックペイロード", "kgstudio.payment.callback-result": "Callback 結果", "kgstudio.payment.callback-status": "コールバックステータス", "kgstudio.payment.callback-type": "コールバックタイプ", "kgstudio.payment.callback-url": "コールバック URL", "kgstudio.payment.callback-url-placeholder": "このURLに支払い結果を送信します", "kgstudio.payment.chain-id": "", "kgstudio.payment.chain-id-desc": "", "kgstudio.payment.client-id": "クライアント ID", "kgstudio.payment.column-setting": "コラム", "kgstudio.payment.copy-button": "「支払いのコピー」ボタン", "kgstudio.payment.copy-link": "支払いリンクをコピー", "kgstudio.payment.create-item-error": "支払い項目を作成できませんでした", "kgstudio.payment.create-item-success": "支払い項目が正常に作成されました", "kgstudio.payment.create-payment": "支払いを作成", "kgstudio.payment.create-product": "製品を作成", "kgstudio.payment.create-product-title": "製品を作成", "kgstudio.payment.crypto-amount": "暗号金額", "kgstudio.payment.crypto-price": "暗号価格", "kgstudio.payment.currency": "通貨", "kgstudio.payment.custom-fields": "カスタムフィールド", "kgstudio.payment.date-range": "日付範囲", "kgstudio.payment.deadline": "締め切り", "kgstudio.payment.delete-item-confirmation": "この支払い項目を削除してもよろしいですか?", "kgstudio.payment.delete-item-title": "支払い項目を削除", "kgstudio.payment.duration": "所要時間", "kgstudio.payment.edit-product-title": "製品を編集", "kgstudio.payment.error-loading-callbacks": "コールバックログを読み込めませんでした", "kgstudio.payment.error-loading-oauth-clients": "OAuth クライアントのロード中にエラーが発生しました", "kgstudio.payment.error-url": "エラー URL", "kgstudio.payment.error-url-placeholder": "エラーリダイレクト URL を入力してください", "kgstudio.payment.event-details": "イベント詳細", "kgstudio.payment.event-type": "イベントタイプ", "kgstudio.payment.export-csv": "CSV をエクスポート", "kgstudio.payment.failed": "失敗", "kgstudio.payment.fiat-amount": "フィアット金額", "kgstudio.payment.fiat-currency": "フィアット通貨", "kgstudio.payment.field-key": "キー", "kgstudio.payment.field-label": "フィールドラベル", "kgstudio.payment.field-label-required": "フィールドラベルが必要です", "kgstudio.payment.field-name": "[フィールド名]", "kgstudio.payment.field-name-duplicate": "", "kgstudio.payment.field-name-required": "フィールド名は必須です", "kgstudio.payment.field-type": "フィールドタイプ", "kgstudio.payment.field-value": "価値", "kgstudio.payment.group-key": "グループキー", "kgstudio.payment.group-key-search-placeholder": "グループキーで検索", "kgstudio.payment.http-code": "HTTP コード", "kgstudio.payment.intent-id": "インテント ID", "kgstudio.payment.kg-deep-link": "KG ディープリンク", "kgstudio.payment.merchant-email": "マーチャントEメール", "kgstudio.payment.merchant-settings": "マーチャント設定", "kgstudio.payment.merchant-settings-desc": "マーチャントのメールアドレスとアクセントカラーが支払いページに表示されます。顧客はメールでマーチャントに連絡してサポートを受けることができます。アクセントカラーは重要な要素を強調しています。", "kgstudio.payment.new-field": "新しいフィールド", "kgstudio.payment.open-page": "支払いページを開く", "kgstudio.payment.optional-field": "オプションフィールド", "kgstudio.payment.optional-fields": "オプションフィールド", "kgstudio.payment.order-data": "注文データ", "kgstudio.payment.order-data-expanded": "注文データを拡張", "kgstudio.payment.order-data-fields": "注文データフィールド", "kgstudio.payment.organization-icon": "組織アイコン", "kgstudio.payment.organization-icon-placeholder": "組織アイコンの URL を入力", "kgstudio.payment.organization-id": "組織 ID", "kgstudio.payment.organization-id-placeholder": "組織 ID を入力", "kgstudio.payment.organization-name": "[組織名]", "kgstudio.payment.organization-name-placeholder": "組織名を入力", "kgstudio.payment.organization-required": "組織が必要です", "kgstudio.payment.payer-address": "支払人住所", "kgstudio.payment.payment-chain-id": "ペイメントチェーン ID", "kgstudio.payment.payment-failed": "支払い失敗", "kgstudio.payment.payment-intent-id": "支払い意図 ID", "kgstudio.payment.payment-item-list": "[製品]", "kgstudio.payment.payment-link": "支払いリンク", "kgstudio.payment.payment-list": "注文履歴", "kgstudio.payment.payment-status": "支払い状況", "kgstudio.payment.payment-success": "支払い成功", "kgstudio.payment.payment-text": "顧客", "kgstudio.payment.payment-tx-hash": "支払い TX ハッシュ", "kgstudio.payment.pay-token": "優先支払いトークン", "kgstudio.payment.pending": "保留中", "kgstudio.payment.pricing-mode": "価格モード", "kgstudio.payment.pricing-mode-dynamic": "ダイナミック", "kgstudio.payment.pricing-mode-fixed": "修正済み", "kgstudio.payment.product-currency": "通貨", "kgstudio.payment.product-description": "[商品説明]", "kgstudio.payment.product-description-placeholder": "製品の説明を入力してください", "kgstudio.payment.product-image": "商品画像", "kgstudio.payment.product-name": "[製品名]", "kgstudio.payment.product-name-placeholder": "製品名を入力してください", "kgstudio.payment.product-price": "製品価格", "kgstudio.payment.product-price-placeholder": "商品価格を入力してください", "kgstudio.payment.received-amount": "受領金額", "kgstudio.payment.refund": "払い戻し", "kgstudio.payment.refund.address.placeholder": "住所を入力", "kgstudio.payment.refund.address.text": "払い戻し住所", "kgstudio.payment.refund-amount": "払い戻し金額", "kgstudio.payment.refund.amount.placeholder": "金額を入力", "kgstudio.payment.refund.amount.text": "金額", "kgstudio.payment.refund.button": "払い戻し", "kgstudio.payment.refund.cancel": "[キャンセル]", "kgstudio.payment.refund.confirm": "払い戻しを確認", "kgstudio.payment.refund.error": "払い戻しの開始中にエラーが発生しました", "kgstudio.payment.refund.success": "払い戻しが正常に開始されました", "kgstudio.payment.refund.title": "返金支払い", "kgstudio.payment.remove-field": "[削除]", "kgstudio.payment.required-field": "必須フィールド", "kgstudio.payment.required-fields": "必須フィールド", "kgstudio.payment.result-failed": "失敗", "kgstudio.payment.result-success": "成功", "kgstudio.payment.search-intent": "インテント ID で検索", "kgstudio.payment.select-client": "[クライアントを選択]", "kgstudio.payment.select-client-first": "最初にクライアントを選択してください", "kgstudio.payment.sending": "送信中...", "kgstudio.payment.send-test": "テストを送信", "kgstudio.payment.sent": "送信済み", "kgstudio.payment.sign-payload": "サインペイロード", "kgstudio.payment.status": "ステータス", "kgstudio.payment.status-code": "ステータスコード", "kgstudio.payment.status-completed": "完了しました", "kgstudio.payment.status-expired": "期限切れ", "kgstudio.payment.status-failed": "失敗", "kgstudio.payment.status-pending": "保留中", "kgstudio.payment.status-refunded": "払い戻し", "kgstudio.payment.success": "成功", "kgstudio.payment.success-message": "", "kgstudio.payment.success-message-desc": "", "kgstudio.payment.success-message-placeholder": "", "kgstudio.payment.success-url": "サクセス URL", "kgstudio.payment.success-url-placeholder": "成功リダイレクト URL を入力してください", "kgstudio.payment.symbol": "シンボル", "kgstudio.payment.test": "テスト", "kgstudio.payment.test-callback": "テストコールバック", "kgstudio.payment.test-callback-error": "テストコールバックを送信できませんでした", "kgstudio.payment.test-callback-sent": "テストコールバックが正常に送信されました", "kgstudio.payment.timestamp": "タイムスタンプ", "kgstudio.payment.type": "タイプ", "kgstudio.payment.type-payment": "支払", "kgstudio.payment.type-test": "テスト", "kgstudio.payment.update-item-error": "支払い項目を更新できませんでした", "kgstudio.payment.update-item-success": "支払い項目が正常に更新されました", "kgstudio.payment.upload-image-desc": "画像をアップロード", "kgstudio.payment.url-copied": "リンクがコピーされました", "kgstudio.payment.url-hint": "リダイレクトするときは、「?」を追加します。この URL に pid=payment_intent_id」", "kgstudio.payment.view-payment-page": "支払いページの共有", "kgstudio.payment.webhook-url": "ウェブフック URL", "kgstudio.permissions.notification-description": "システムにアクセスする権限を持つユーザーを表示できます。ユーザーを編集したい場合は、システムプロバイダーに連絡してください。", "kgstudio.permissions.title": "ユーザ", "kgstudio.permissions.user-id": "ユーザ ID", "kgstudio.review.ai-summary": "人工知能サマリー", "kgstudio.review.ai-summary-fail": "このお客様に関するネガティブなニュースはなく、AI Summaryを実行できません。", "kgstudio.review.aml-risk": "AML リスク", "kgstudio.review.btn": "レビュー", "kgstudio.review.case-details": "ケース詳細", "kgstudio.review.check-id": "ID をチェック", "kgstudio.review.created": "作成されました", "kgstudio.review.details": "[詳細]", "kgstudio.review.filter-placeholder": "正式名称、電話番号、電子メール、国民ID、LINE ID", "kgstudio.review.high-risk": "ハイリスク", "kgstudio.review.idv": "IDV", "kgstudio.review.id-verification": "ID 検証", "kgstudio.review.idv-fail": "失敗", "kgstudio.review.idv-pass": "パス", "kgstudio.review.internal-note": "内部メモ", "kgstudio.review.internal-notes-length-limit": "内部メモの最大文字数は50文字です", "kgstudio.review.internal-notes-required": "ステータスが「拒否」の場合は、内部メモが必要です", "kgstudio.review.kyc-status": "本人確認ステータス", "kgstudio.review.latest-submit": "最終送信時間", "kgstudio.review.low-risk": "低リスク", "kgstudio.review.mid-risk": "中程度のリスク", "kgstudio.review.name": "[名前]", "kgstudio.review.name-screening": "名前スクリーニング", "kgstudio.review.personal-information": "個人情報", "kgstudio.review.process-ai": "AI サマリー処理...", "kgstudio.review.processing": "処理", "kgstudio.review.review-detail": "レビュー詳細", "kgstudio.review.reviewer": "レビュアー", "kgstudio.review.review-result": "レビュー結果", "kgstudio.review.review-time": "レビュー時間", "kgstudio.review.risk": "リスク", "kgstudio.review.sanctioned": "制裁を受けた", "kgstudio.review.sanctioned-false": "偽", "kgstudio.review.sanctioned-true": "本当", "kgstudio.review.start-ai": "AI 概要を開始", "kgstudio.review.submission": "サブミッション", "kgstudio.review.summary": "サマリー", "kgstudio.review.updated": "更新日", "kgstudio.search.email": "メール検索", "kgstudio.select.at-least-one": "少なくとも 1 つのアイテムを選択してください", "kgstudio.send.add-attachment": "添付ファイルを追加", "kgstudio.send.attachment-required": "承認には添付が必要", "kgstudio.send.by-email": "Eメールで", "kgstudio.send.by-phone-number": "電話番号で", "kgstudio.send.do-not-leave-page": "このページを離れないでください。", "kgstudio.send.gasless-alert-button": "", "kgstudio.send.gasless-alert-desc": "", "kgstudio.send.gasless-alert-title": "", "kgstudio.send.gasless-modal-deducted-desc-1": "", "kgstudio.send.gasless-modal-deducted-desc-2": "", "kgstudio.send.gasless-modal-deducted-title": "", "kgstudio.send.gasless-modal-desc": "", "kgstudio.send.gasless-modal-full-title": "", "kgstudio.send.gasless-modal-title": "", "kgstudio.send.gas-token-balance": "ガストークン残高:{balance}(推定ガス料金:{gasFee})", "kgstudio.send.loading-hint": "ブロックチェーンが非常に混雑している場合、トランザクションに予想よりも時間がかかることがあります。トランザクションのステータスと詳細を確認するには、Tx Hashをクリックしてください。", "kgstudio.send.max": "", "kgstudio.send.note-placeholder": "トランザクションノートを入力し、承認に必要な添付ファイルをアップロードします。", "kgstudio.send.note-required": "承認には取引メモが必要", "kgstudio.send.over-limit": "オーバー・ザ・リミット", "kgstudio.send.remaining-balance": "今日の残りの振替可能残高: ", "kgstudio.send.remaining-balance-today": "{formattedCurrentLimit}今日の残高はU（{formattedDailyLimit}1日の上限はU）です。", "kgstudio.send.review-note": "レビューメモ", "kgstudio.send.send-confirm-alert": "この取引が確認されると、すぐに有効になり、元に戻すことはできません！ブロックチェーン、金額、受取人が正しいことを確認してください。", "kgstudio.send.send-confirm-submit-desc-alert": "送信後は変更できないため、取引の詳細が正しいことを確認してください。", "kgstudio.send.send-confirm-submit-title-alert": "この取引は承認後にのみ送信されます。", "kgstudio.send.send-to": "に送信", "kgstudio.send.submit-request": "リクエストを送信", "kgstudio.send.title": "資金を送る", "kgstudio.send.to-user": "ユーザーへ", "kgstudio.send.transaction-attachment": "アタッチメント", "kgstudio.send.transaction-error-desc": "管理者が十分な資産を預けた後、再試行してください", "kgstudio.send.transaction-error-title": "エラー:在庫が不足しています", "kgstudio.send.transaction-note": "トランザクションノート", "kgstudio.send.transaction-submit-success-desc": "取引が送信され、承認およびリリースされた後に実行されます。", "kgstudio.send.transaction-submit-success-title": "トランザクションが送信されました", "kgstudio.send.transaction-success-desc": "取引リクエストは正常に送信され、間もなく取引結果を確認できるようになります。", "kgstudio.send.transaction-success-title": "トランザクションは正常に送信されました", "kgstudio.send.tx-failed": "トランザクション失敗", "kgstudio.send.tx-failed-description": "送金に失敗しました。再試行するか、システム管理者に連絡してください。", "kgstudio.send.tx-in-progress": "トランザクションが進行中です。", "kgstudio.send.tx-success": "トランザクション成功！", "kgstudio.send.view-profile": "プロフィールを表示", "kgstudio.setting.api-keys": "API キー", "kgstudio.setting.create-api-key": "API キーの作成", "kgstudio.setting.create-first-api-key": "最初の API キーを作成して始めましょう", "kgstudio.setting.create-first-oauth-client": "最初の OAuth クライアントを作成して始めましょう", "kgstudio.setting.create-oauth-client": "OAuth クライアントの作成", "kgstudio.setting.no-api-keys": "API キーなし", "kgstudio.setting.no-oauth-clients": "OAuth クライアントはありません", "kgstudio.setting.oauth-clients": "OAuth クライアント", "kgstudio.setting.org-settings": "組織設定", "kgstudio.setting.user.account-settings": "アカウント設定", "kgstudio.setting.user.add-address": "住所を追加", "kgstudio.setting.user.add-new-address": "新しい住所を追加", "kgstudio.setting.user.address-name": "住所名", "kgstudio.setting.user.address-name-placeholder": "例:私のメインウォレット", "kgstudio.setting.user.api-keys": "API キー", "kgstudio.setting.user.api-keys-description": "API キーを使うと、KryptoGo Studio アカウントにプログラム的にアクセスすることができます。これらは安全に保管し、決して公開しないでください。", "kgstudio.setting.user.cancel": "[キャンセル]", "kgstudio.setting.user.check-documentation": "ドキュメンテーションを確認", "kgstudio.setting.user.client-domain": "ドメイン", "kgstudio.setting.user.client-id": "クライアント ID", "kgstudio.setting.user.client-id-desc": "クライアント ID をお持ちでない場合は、[アカウント設定-> OAuth クライアント] ページに追加してください。", "kgstudio.setting.user.client-id-required": "クライアント ID が必要です", "kgstudio.setting.user.client-name": "クライアント名", "kgstudio.setting.user.client-secret": "クライアントシークレット", "kgstudio.setting.user.client-type": "タイプ", "kgstudio.setting.user.close": "閉じる", "kgstudio.setting.user.confirm-delete-api-key": "この API キーを削除してよろしいですか?このアクションは元に戻せません。", "kgstudio.setting.user.confirm-delete-oauth-client": "この OAuth クライアントを削除してよろしいですか?このアクションは元に戻せません。", "kgstudio.setting.user.confirm-deletion": "削除を確認", "kgstudio.setting.user.copy-prompt": "[プロンプトをコピー]", "kgstudio.setting.user.create-api-key": "API キーの作成", "kgstudio.setting.user.create-client-title": "OAuth クライアントの作成", "kgstudio.setting.user.created": "作成されました", "kgstudio.setting.user.create-first-api-key": "最初の API キーを作成して始めましょう", "kgstudio.setting.user.create-first-oauth-client": "最初の OAuth クライアントを作成して始めましょう", "kgstudio.setting.user.create-key-title": "API キーの作成", "kgstudio.setting.user.create-oauth-client": "OAuth クライアントの作成", "kgstudio.setting.user.default": "デフォルト", "kgstudio.setting.user.delete": "[削除]", "kgstudio.setting.user.description": "[説明]", "kgstudio.setting.user.domain-format-note": "ドメインは http://または https://で始まる必要があります (例:https://app.kryptogo.com)", "kgstudio.setting.user.edit": "[編集]", "kgstudio.setting.user.error.add-address": "フォーマットエラー、EVM 支払い住所を追加できません", "kgstudio.setting.user.error.copy-clipboard": "クリップボードへのコピーに失敗しました", "kgstudio.setting.user.error.create-api-key": "API キーを作成できませんでした", "kgstudio.setting.user.error.create-oauth-client": "OAuth クライアントを作成できませんでした", "kgstudio.setting.user.error.delete-api-key": "API キーを削除できませんでした", "kgstudio.setting.user.error.domain-format": "ドメインは http://または https://で始まる必要があります", "kgstudio.setting.user.error.domain-required": "ドメインが必要です", "kgstudio.setting.user.error.fetch-api-keys": "API キーを取得できませんでした", "kgstudio.setting.user.error.fetch-oauth-clients": "OAuth クライアントを取得できませんでした", "kgstudio.setting.user.error.set-default-address": "既定の住所を設定できませんでした", "kgstudio.setting.user.error.update-oauth-client": "OAuth クライアントを更新できませんでした", "kgstudio.setting.user.joyride.back": "戻る", "kgstudio.setting.user.joyride.close": "閉じる", "kgstudio.setting.user.joyride.finish": "フィニッシュ", "kgstudio.setting.user.joyride.next": "[次へ]", "kgstudio.setting.user.joyride.skip": "スキップ", "kgstudio.setting.user.joyride.step1": "OAuth クライアント ID (クライアント ID) を使用すると、アプリケーションを KryptoGO 決済 SDK サービスと統合できます。また、独自のクライアント ID を作成してさまざまなアプリケーションに統合することもできます。", "kgstudio.setting.user.joyride.step2": "AIプロンプトをワンクリックでコピーし、推奨AIモデルサービス（claude-3.7-sonnect、gpt-o3）に貼り付けると、すぐに使用できる暗号決済Webページが生成されます。", "kgstudio.setting.user.key-description": "説明 (オプション)", "kgstudio.setting.user.key-name": "[名前]", "kgstudio.setting.user.key-prefix": "キープレフィックス", "kgstudio.setting.user.last-used": "最終使用日", "kgstudio.setting.user.loading": "読み込み中...", "kgstudio.setting.user.logo": "[ロゴ]", "kgstudio.setting.user.manage-payment-addresses": "支払い住所の管理", "kgstudio.setting.user.no-api-keys": "API キーなし", "kgstudio.setting.user.no-oauth-clients": "OAuth クライアントはありません", "kgstudio.setting.user.oauth-clients": "OAuth クライアント", "kgstudio.setting.user.oauth-clients-description": "OAuth クライアントを使用すると、アプリケーションを KryptoGo サービスと統合できます。各クライアントには固有の ID とシークレットがあり、安全に保管する必要があります。", "kgstudio.setting.user.org-wallet": "組織ウォレット", "kgstudio.setting.user.payment-address-description": "支払いアドレスは、顧客からの支払いを受け取るために使用されます。", "kgstudio.setting.user.save-client-message": "クライアントシークレットが表示されるのはこのときだけです。コピーして安全に保管してください。クライアントシークレットは、KryptoGO サービスに API リクエストを行う際に、アプリケーションを認証するために使用されます。", "kgstudio.setting.user.save-client-title": "重要:クライアントの認証情報を保存してください", "kgstudio.setting.user.save-key-message": "この API キーが表示されるのはこのときだけです。コピーして安全に保管してください。", "kgstudio.setting.user.save-key-title": "重要:API キーを保存してください", "kgstudio.setting.user.set-as-default": "[既定として設定]", "kgstudio.setting.user.success.address-added": "EVM 支払い住所が正常に追加されました！", "kgstudio.setting.user.success.api-key-created": "API キーが正常に作成されました", "kgstudio.setting.user.success.api-key-deleted": "API キーは正常に削除されました", "kgstudio.setting.user.success.copied": "{item}クリップボードにコピー", "kgstudio.setting.user.success.oauth-client-created": "OAuth クライアントは正常に作成されました", "kgstudio.setting.user.success.oauth-client-updated": "OAuth クライアントは正常に更新されました", "kgstudio.setting.user.success.org-wallet-default": "デフォルトとして設定された組織ウォレット", "kgstudio.setting.user.update-oauth-client": "OAuth クライアントの更新", "kgstudio.setting.user.wallet-address": "ウォレットアドレス", "kgstudio.setting.user.your-addresses": "あなたの住所", "kgstudio.team.delete.description": "{currentMemberName}({currentMemberEmail})を無効にしてよろしいですか?ユーザーはすぐにログアウトされ、再びログインすることはできません。", "kgstudio.team.delete.success": "「{name}({email})」はチームから削除されました。", "kgstudio.team.edit.title": "チームメンバーを編集", "kgstudio.team.invite.error.existed": "ユーザーは既に存在します", "kgstudio.team.invite.error.rate-limit": "短期間で招待数の上限に達しました。後でもう一度試してください。", "kgstudio.team.invite.sent": "招待状が送信されました！", "kgstudio.team.invite.text": "招待", "kgstudio.team.invite.title": "チームメンバーを招待", "kgstudio.team.member.member-id.title": "メンバー ID (オプション)", "kgstudio.team.member.name.placeholder": "例:アンナ・ワン", "kgstudio.team.members": "チームメンバー", "kgstudio.team.name.validation.required": "名前を空にすることはできません", "kgstudio.team.permission-settings": "権限設定", "kgstudio.team.rate-limit.hint": "1 分後に再招待", "kgstudio.team.remove-member": "メンバーを非アクティブ化", "kgstudio.team.resend": "招待状を再送信", "kgstudio.team.role.approver": "承認者", "kgstudio.team.role.aseet-pro": "アセットプロロール", "kgstudio.team.role.asset-pro": "アセットプロロール", "kgstudio.team.role.compliance": "コンプライアンスロール", "kgstudio.team.role-decription.aseet-pro.admin": "財務情報の表示、アセットの送信、承認、すべての取引履歴のダウンロード、メンバーの設定など、AssetProへのフルアクセスが可能", "kgstudio.team.role-decription.aseet-pro.approver": "AssetPro のすべてのトランザクションを表示し、承認待ちのトランザクションを承認または拒否できます。", "kgstudio.team.role-decription.aseet-pro.finance-manager": "AssetProのすべてのトランザクションを表示したり、リリース待ちのトランザクションをリリースまたは拒否したり、トレジャリーを表示したりできます。", "kgstudio.team.role-decription.aseet-pro.trader": "1日の限度額を設定してアセットを送信し、自分のトランザクションを確認できる", "kgstudio.team.role-decription.compliance.admin": "レビュー、検証プロセスのグローバル設定など、コンプライアンスへのフルアクセスが可能", "kgstudio.team.role-decription.compliance.reviewer": "コンプライアンスケースの確認と編集のみ可能", "kgstudio.team.role.description": "所有者は、組織がサブスクライブしているモジュールのすべての機能に完全にアクセスできます。", "kgstudio.team.role.finance-manager": "ファイナンスマネージャー", "kgstudio.team.role.nft-boost": "NFT ブーストの役割", "kgstudio.team.role.owner": "オーナー", "kgstudio.team.role.reviewer": "レビュアー", "kgstudio.team.role.title": "チームロール", "kgstudio.team.role.trader": "トレーダー", "kgstudio.team.role.user360": "ユーザ 360 ロール", "kgstudio.team.role.validation": "モジュールロールを少なくとも 1 つ選択してください", "kgstudio.team.role.wallet": "ウォレットロール", "kgstudio.team.text": "チーム", "kgstudio.time.end-after-start": "終了時刻は開始時刻より後でなければなりません", "kgstudio.time.input-time": "時間を入力してください", "kgstudio.transaction.export": "[エクスポート]", "kgstudio.transaction.operator": "オペレータ", "kgstudio.transaction.placeholder": "受信者の電子メール、電話番号、住所、Tx ハッシュ", "kgstudio.transactions.recipient-placeholder": "電子メール、電話、住所", "kgstudio.transaction.status-awaiting-approval": "承認待ち", "kgstudio.transaction.status-awaiting-release": "リリース待ち", "kgstudio.transaction.status-failed": "送信失敗", "kgstudio.transaction.status-pending": "承認待ち ", "kgstudio.transaction.status-reject": "拒否", "kgstudio.transaction.status-rejected": "拒否", "kgstudio.transaction.status-sending": "送信中", "kgstudio.transaction.status-success": "送信成功", "kgstudio.transactions.title": "トランザクション履歴", "kgstudio.transaction.submit-time": "送信時間", "kgstudio.transactions.user-placeholder": "ID、アドレス", "kgstudio.treasury.add-fund": "ファンドを追加", "kgstudio.treasury.add-fund-modal.desc": "QRコードをスキャンしてアセットをこのウォレットアドレスに送信", "kgstudio.treasury.agree-and-continue": "リスクはわかる", "kgstudio.treasury.asset": "資産", "kgstudio.treasury.asset-name": "資産名", "kgstudio.treasury.buy-crypto.desc": "クリプト・デスクを購入", "kgstudio.treasury.buy-crypto.margin": "仮想通貨の利益率を購入 (%)", "kgstudio.treasury.buy-crypto-title": "クリプトを購入", "kgstudio.treasury.chart": "チャート", "kgstudio.treasury.click-to-refresh": "クリックして更新", "kgstudio.treasury.click-to-reveal": "下のボタンをクリックしてプライベートキーを表示してください", "kgstudio.treasury.contract-address": "契約住所", "kgstudio.treasury.deposit-tooltip": "より低いバランス {alert_threshold}{symbol}！取引を確実に成功させるために資金を追加してください。", "kgstudio.treasury.gas-swap.desc": "ガススワップ説明", "kgstudio.treasury.gas-swap.margin": "ガススワップ利益率 (%)", "kgstudio.treasury.gas-swap-title": "ガススワップ", "kgstudio.treasury.liquidity-settings": "流動性設定", "kgstudio.treasury.liquidity-settings-min-max": "最小:{min}%、最大:{max}%。", "kgstudio.treasury.liquidity-type": "流動性タイプ", "kgstudio.treasury.liquidity-update-confirm.btn": "はい", "kgstudio.treasury.liquidity-update-confirm.message": "すべてのお客様に新しい見積もりが表示されます。", "kgstudio.treasury.liquidity-update-confirm.text": "利益率を更新してもよろしいですか？", "kgstudio.treasury.liquidity-update-success": "正常に更新されました！", "kgstudio.treasury.network": "[ネットワーク]", "kgstudio.treasury.price": "価格", "kgstudio.treasury.profit-current-rate": "現在の利益率", "kgstudio.treasury.profit-description": "[説明]", "kgstudio.treasury.profit-margin": "利益率", "kgstudio.treasury.profit-margin-desc": "動的価格設定を使用して、市場価格、処理手数料、利益率に基づいてユーザーへの支払いを計算します。", "kgstudio.treasury.profit-margin-rate": "利益率率", "kgstudio.treasury.profit-margin-setting": "利益率設定", "kgstudio.treasury.profit-rate-edit": "利益率を編集", "kgstudio.treasury.profit-rate-edit-title": "利益率を設定 (%)", "kgstudio.treasury.profit-rate-setting": "利益率設定", "kgstudio.treasury.profit-rate-update-success": "正常に更新されました！", "kgstudio.treasury.profit-rate-update-text": "利益率を更新してもよろしいですか？", "kgstudio.treasury.profit-service": "サービス", "kgstudio.treasury.profit-service-bridge": "ブリッジ（DeFiでのクロスチェーンスワップ）", "kgstudio.treasury.profit-service-bridge-desc1": "Tron、Ethereum、Arbitrum、Polygon、Base、Optimism、Binance Smart Chain間のクロスチェーンスワップ。例：USDT(Tron)をETH(Arbitrum)にスワップすると、収益として元のトークンUSDT(Tron)を獲得します。", "kgstudio.treasury.profit-service-bridge-desc2": "例：顧客が$100相当のUSDT(Tron)を支払い、ETH(Arbitrum)にクロスチェーンスワップする場合。ビジネスが利益率を2％に設定すると、顧客は100 * (1 - 2％) = 98 USD相当のETH(Arbitrum)を受け取ります。", "kgstudio.treasury.profit-service-bridge-hint1": "顧客が$100分のスワップを行うごとに、$nを獲得します。利益率が2％の場合、n = (100) - 100 * (1 - 2％) = 2 USDです。", "kgstudio.treasury.profit-service-bridge-info1": "分散型トークンスワップの利益率（％）を設定します。この率は、顧客が受け取るトークンの量を減少させます。", "kgstudio.treasury.profit-service-bridge-info2": "*流動性は分散型サービスによって提供されます。", "kgstudio.treasury.profit-service-buy": "購入", "kgstudio.treasury.profit-service-buy-desc1": "利益率を 1% に設定し、顧客が暗号通貨の購入に100米ドルを費やした場合\n=> お客様は100/ (1+7％+1％) = 92.59Uを獲得し、収益として米ドルを獲得することになります。", "kgstudio.treasury.profit-service-buy-desc2": "（1％は利益率、7％は購入サービスの市場コストです）", "kgstudio.treasury.profit-service-buy-hint1": "トークンを購入する場合の利益率 (%) を設定します。このレートはトークンの市場価格に加算されます。", "kgstudio.treasury.profit-service-buy-info1": "顧客が100ドル使うごとにnドルを獲得できます。", "kgstudio.treasury.profit-service-buy-info2": "{profit_rate}{profit_usd}利益率 ={profit_rate}% の場合、n = 100/ (1 +7%) -100/ (1 +7% +%) = 米ドル", "kgstudio.treasury.profit-service-send": "送信 (with TRX)", "kgstudio.treasury.profit-service-send-batch": "送信 (バッチ)", "kgstudio.treasury.profit-service-send-batch-desc1": "（バッチ（コレクト）送信サービスでは、初期ネットワーク料金のみが請求されます。今すぐ設定する利益率はありません)", "kgstudio.treasury.profit-service-send-desc1": "顧客がTronでトークンを友人に送り、その友人がガストークンとして十分なTRXを持っている場合、ガストークン（TRX）の一部を収益として獲得できます。(「送信」の利益率はTronでのみ有効です。)", "kgstudio.treasury.profit-service-send-desc2": "例えば、ある顧客が昼食代として12.5 USDT（TRC20）を友人に送りたいと思っていて、最初のガス料金が3 TRXでした。企業は利益率を 10% に設定しました。 \n=> 友人は12.5米ドルを受け取り、顧客は12.5米ドルと3.3トルコペソをガス手数料として送金します。ガス料金は3* (1 + 10％) = 3.3TRXになるからです。", "kgstudio.treasury.profit-service-send-gasless": "送信 (Gasless)", "kgstudio.treasury.profit-service-send-gasless-desc1": "顧客がガストークンとして十分なTRXを持っていないのにTronでトークンを友人に送信した場合、その顧客はTRC20トークンを送信するだけで、ソーストークンの一部、たとえばUSDTを収益として獲得できます。（「ガスレス送信」の利益率はTronでのみ有効です）", "kgstudio.treasury.profit-service-send-gasless-desc2": "例えば、ある顧客が昼食代として12.5 USDT（TRC20）を友人に送りたいと思っていて、最初のガス料金が3 TRXでした。企業は利益率を 10% に設定しました。 \n=> 友人は12.5 USDTを受け取り、顧客は合計14.26USDTを受け取ります。 \n=> 初期費用と取引全体をカバーするには、スマートコントラクトにはガス手数料として10 TRXが必要です。 \n=> ガスサービス料 = 10 * (1 + 10％) = 11 トルコペソ ~ 1.76 米ドル \n=> お客様は1.76 + 12.5 = 14.26米ドルを送金します。", "kgstudio.treasury.profit-service-send-gasless-hint1": "ガスレス送信の利益率 (%) を設定します。このレートは、顧客のウォレットから送信されるソーストークンの実際の量に影響します。", "kgstudio.treasury.profit-service-send-gasless-info1": "ネットワークの混雑状況によっては、顧客が送信するTronトランザクションごとに$nを獲得できる場合があります。", "kgstudio.treasury.profit-service-send-gasless-info2": "{profit_usd}利益率 ={profit_rate}% の場合、n = 10 *{profit_rate}% = {profit_trx}TRX ~ 米ドル", "kgstudio.treasury.profit-service-send-hint1": "トークンを送信する場合の利益率 (%) を設定します。このレートにより、顧客が支払うガス料金が増加します。", "kgstudio.treasury.profit-service-send-hint2": "*TRC20-USDTトランザクションのみサポートされています。", "kgstudio.treasury.profit-service-send-info1": "ネットワークの混雑状況によっては、顧客が送信するTronトランザクションごとに$nを獲得できる場合があります。", "kgstudio.treasury.profit-service-send-info2": "{profit_usd}利益率 ={profit_rate}% の場合、n = 3*{profit_rate}% = {profit_trx}TRX ~ 米ドル", "kgstudio.treasury.profit-service-swap-cefi": "スワップ (<PERSON><PERSON><PERSON>(AssetPro))", "kgstudio.treasury.profit-service-swap-cefi-desc1": "トロン (TRC20) トークンのみ。顧客がガススワップリクエスト (USDT (Tron)-> TRX) を送信すると、ターゲットトークン (TRX)) を収入として獲得できます。", "kgstudio.treasury.profit-service-swap-cefi-desc2": "例えば、ある顧客がTRXと交換するために100米ドル相当のUSDTを支払ったとします。また、ブロックチェーンのエネルギーレンタル費用は5米ドルでした。企業が利益率を 10% に設定した場合、 \n=> お客様には、(100-5) * (1-10％) = 85.5米ドル相当の取引高が付与されます。", "kgstudio.treasury.profit-service-swap-cefi-hint1": "集中型トークンスワップの利益率 (%) を設定します。このレートにより、顧客が受け取るトークンの量が減ります。", "kgstudio.treasury.profit-service-swap-cefi-hint2": "*流動性はAssetProトレジャリーから提供されます。", "kgstudio.treasury.profit-service-swap-cefi-info1": "ネットワークの混雑状況によっては、顧客が100ドルの取引量を交換するごとにnドルを獲得できる場合があります。", "kgstudio.treasury.profit-service-swap-cefi-info2": "利益率 ={profit_rate}% の場合、n = (100-5)-[(100-5) * (1-{profit_rate}%)] = {profit_usd}米ドル", "kgstudio.treasury.profit-service-swap-defi": "スワップ (DeFi)", "kgstudio.treasury.profit-service-swap-defi-desc1": "流動性はサードパーティのDeFiサービスからのもので、イーサリアム、アービトラム、ポリゴン、バイナンススマートチェーンをサポートします。たとえば、SUSHIをPOLにスワップすると、ソーストークン（SUSHI）の米ドル価値が収益として得られます。", "kgstudio.treasury.profit-service-swap-defi-desc2": "例えば、お客様がPOLと交換するために100米ドル相当のSUSHIを支払いました。企業が利益率を 10% に設定した場合、 \n=> お客様には、100* (1-10％) = 90米ドルに相当するPOLが提供されます。", "kgstudio.treasury.profit-service-swap-defi-hint1": "分散型トークンスワップの利益率 (%) を設定します。このレートを上げると、顧客が受け取るトークンの量が減ります。", "kgstudio.treasury.profit-service-swap-defi-hint2": "*分散型サービスによって提供される流動性。", "kgstudio.treasury.profit-service-swap-defi-info1": "お客様が100ドルの取引量を交換するごとに、nドルを獲得できます。", "kgstudio.treasury.profit-service-swap-defi-info2": "{profit_usd}利益率 ={profit_rate}% の場合、n = (100)-100* (1-{profit_rate}%) = 米ドル", "kgstudio.treasury.quantity": "数量", "kgstudio.treasury.retrieve-balance-error": "", "kgstudio.treasury.reveal-seedphrase": "プライベートキーを公開", "kgstudio.treasury.seedphrase-warning-1": "プライベートキーを使用すると、ウォレットと資金に完全にアクセスできます。", "kgstudio.treasury.seedphrase-warning-2": "プライベートキーを他人と共有したり、デジタルで保存したりしないでください。", "kgstudio.treasury.seedphrase-warning-3": "KryptoGO は、秘密鍵や資金を紛失したり盗まれたりした場合、それを回復することはできません。", "kgstudio.treasury.seedphrase-warning-4": "KryptoGO は、秘密鍵の公開に起因するいかなる損失についても責任を負いません。", "kgstudio.treasury.seedphrase-warning-title": "重要なセキュリティ警告", "kgstudio.treasury.token": "トークン", "kgstudio.treasury.token-price": "トークンの価格", "kgstudio.treasury.value": "価値", "kgstudio.user360.wallet-usage-registered": "アクティベート", "kgstudio.user360.wallet-usage-unregistered": "非アクティブ", "kgstudio.user-dna.7-day-active": "7 日間アクティブ", "kgstudio.user-dna.applied_at": "適用日", "kgstudio.user-dna.app-open-times": "アプリオープン時間", "kgstudio.user-dna.approved": "承認済み", "kgstudio.user-dna.approved_at": "承認日", "kgstudio.user-dna.dapp-favorites": "DAppのお気に入り", "kgstudio.user-dna.delay-hint": "ウォレットアドレスと残高情報は、ブロックチェーンのアクティビティレベルが原因で遅延することがあります。", "kgstudio.user-dna.dob": "生年月日", "kgstudio.user-dna.email": "電子メール", "kgstudio.user-dna.first-apply": "最初に申し込む", "kgstudio.user-dna.first-web3-activity": "最初の Web3 アクティビティ", "kgstudio.user-dna.last-active": "最終アクティブ", "kgstudio.user-dna.last-apply": "最終申請", "kgstudio.user-dna.last-login": "最終ログイン", "kgstudio.user-dna.low": "低", "kgstudio.user-dna.name": "[名前]", "kgstudio.user-dna.nation": "国籍", "kgstudio.user-dna.national-id": "ネイション ID", "kgstudio.user-dna.non-kyc-user": "非KYCユーザー", "kgstudio.user-dna.no-wallet": "このお客様は現在ウォレットを持っていません。", "kgstudio.user-dna.of-10k-users": "1万人のユーザー", "kgstudio.user-dna.phone": "電話", "kgstudio.user-dna.real-name": "リアルネーム", "kgstudio.user-dna.registered": "ダウンロードおよびログイン", "kgstudio.user-dna.risk-score": "リスクスコア", "kgstudio.user-dna.sign-times": "サインタイム", "kgstudio.user-dna.status": "本人確認ステータス", "kgstudio.user-dna.submission": "サブミッション", "kgstudio.user-dna.title": "ユーザープロフィール", "kgstudio.user-dna.transactions-volume": "トランザクションボリューム", "kgstudio.user-dna.tvl": "TVL", "kgstudio.user-dna.wallet-activity": "ウォレットアクティビティ", "kgstudio.user-dna.wallet-app-activities": "ウォレットアプリのアクティビティ", "kgstudio.user-dna.wallets": "ウォレット", "kgstudio.user-dna.wallet.tag": "タグ", "kgstudio.validation.correct-format": "正しい形式を入力してください", "kgstudio.validation.number-greater-than-zero": "0 より大きい値を入力してください", "kgstudio.validation.phone-or-email-required": "資金は、ウォレットサービスに登録されているメールアドレスまたは電話番号にのみ送金できます。", "kgstudio.validation.required": "値を入力してください", "kgstudio.validation.sorrect-format": "正しい形式を入力してください", "kgstudio.validation.valid-address": "有効なアドレスを入力してください", "kgstudio.validation.valid-email": "有効なメールアドレスを入力してください", "kgstudio.validation.valid-phone": "有効な電話番号を入力してください", "kgstudio.wallet.active-features": "アクティブ機能", "kgstudio.wallet.app-images.app-icon": "アプリアイコン", "kgstudio.wallet.app-images.splash": "スプラッシュ", "kgstudio.wallet.app-images.title": "アプリ画像", "kgstudio.wallet.app-settings": "アプリ設定", "kgstudio.wallet.app-under-review": "アプリは審査中であり、変更できません。", "kgstudio.wallet.button.view-demo": "デモを見る", "kgstudio.wallet.config.android": "アンドロイド (グーグルプレイ)", "kgstudio.wallet.config.app-image": "アプリ画像", "kgstudio.wallet.config.app-startup": "アプリ起動画面", "kgstudio.wallet.config.app-store-info": "アプリストア情報", "kgstudio.wallet.config.brand-logo-alt": "ブランドロゴ", "kgstudio.wallet.config.check": "チェック", "kgstudio.wallet.config.completion.step1": "KryptoGo Walletを開き、下のQRコードをスキャンしてウォレットアプリをプレビューしてください", "kgstudio.wallet.config.completion.step2": "調整が必要な場合は、前の手順に戻って設定を変更してください", "kgstudio.wallet.config.completion.step3": "設定が正しいことを確認したら、下のボタンをクリックして送信してください", "kgstudio.wallet.config.completion.title": "アプリの設定が完了しました！", "kgstudio.wallet.config.configure-later": "後で設定", "kgstudio.wallet.config.configure-publish-data": "公開データを設定する", "kgstudio.wallet.config.confirm-before-submit": "提出する前に、すべてのグラフィックとテキストが正しいことを確認してください", "kgstudio.wallet.config.confirm-submit": "アプリのプロダクションバージョンを確認して送信する", "kgstudio.wallet.config.data-verification": "入力した情報を確認してください", "kgstudio.wallet.config.desc.all-chains-desc": "イーサリアム、ポリゴン、BNBチェーン、アービトラム、KCC、ローニン、ビットコイン、ソラナ、トロンなど、KryptoGoがサポートするすべてのチェーン。KryptoGo がサポート対象のチェーンを更新すると、ウォレットも自動的に更新されます。", "kgstudio.wallet.config.desc.all-evm-chains-desc": "イーサリアム、ポリゴン、BNBチェーン、アービトラム、KCC、ローニンなど、KryptoGO がサポートするすべてのEVMチェーン。KryptoGOポートされている EVM チェーンを更新すると、ウォレットも自動的に更新されます。", "kgstudio.wallet.config.desc.currency-desc": "ホームページにはスワップ機能ボタンが表示され、ユーザーはさまざまな取引ペアを交換できます（ETH、Polygon、BNBシングルチェーンスワップをサポート）。", "kgstudio.wallet.config.desc.custom-desc": "KryptoGO がサポートしているチェーンから、表示したいチェーンを選択します。注意:KryptoGO がサポートしているチェーンを更新しても、ウォレットは自動的に更新されません。自分で設定を変更する必要があります。", "kgstudio.wallet.config.desc.custom-token-description": "ウォレットのトークンリストにカスタムトークンを追加し、価格を設定できます。カスタムトークンが不要な場合は、この質問は省略できます。", "kgstudio.wallet.config.desc.dapp-list": "アプリが公開されたら、いつでもDAppリストを更新できます。現時点でDAppリストをカスタマイズする必要がない場合は、このリストをスキップしてください。", "kgstudio.wallet.config.desc.defi-desc": "デファイ", "kgstudio.wallet.config.desc.displayed-asset-type-desc": "資産タイプは、ウォレットのホームページに表示されるものです。", "kgstudio.wallet.config.desc.displayed-chains-desc": "選択されていないチェーンはサポートされず、ウォレットに表示されません。", "kgstudio.wallet.config.desc.english": "EN-US (デフォルト)", "kgstudio.wallet.config.desc.explore-dapp-browser-desc": "ユーザーは、ウォレットに組み込まれているDAppブラウザを介してDApp操作に接続できます。", "kgstudio.wallet.config.desc.japanese": "日本語", "kgstudio.wallet.config.desc.kyc-user-verification-desc": "Compliance Pro機能と組み合わせることで、ユーザーはKYCデータを送信でき、本人確認やデューデリジェンスを行うことができます。", "kgstudio.wallet.config.desc.languages": "ウォレットアプリ言語", "kgstudio.wallet.config.desc.login-methods": "ログイン方法", "kgstudio.wallet.config.desc.nft-desc": "ETHとポリゴンのNFTをサポートします。", "kgstudio.wallet.config.desc.nft-rewards-desc": "NFT Boost機能と組み合わせて、ユーザーに交換可能なNFTエンパワーメントを提供します。", "kgstudio.wallet.config.desc.nft-sell-desc": "NFTページには販売ボタンが表示され、ユーザーはワンクリックでOpenSeaにNFTを一覧表示できます（DAppブラウザー機能が必要です）。", "kgstudio.wallet.config.desc.show-poap-desc": "収集品には、ユーザーが所有するPOAP NFTが表示されます。", "kgstudio.wallet.config.desc.simplified-chinese": "中文（简体）", "kgstudio.wallet.config.desc.support-info": "資産タイプは、ウォレットのホームページに表示されるものです。選択されていない資産タイプはユーザーのウォレットには表示されません。", "kgstudio.wallet.config.desc.swap-desc": "ホームページにはスワップ機能ボタンが表示され、ユーザーはさまざまなブロックチェーントークンを交換できます。(ETH、ポリゴン、BNB、アービトラムのシングルチェーンスワップに対応。法定通貨取引所は含まれていません。)", "kgstudio.wallet.config.desc.theme": "ウォレットアプリにブランドカラーを適用する", "kgstudio.wallet.config.desc.traditional-chinese": "中文（繁體）", "kgstudio.wallet.config.design-guideline": "アプリアイコンデザインガイドライン", "kgstudio.wallet.config.extension": "ウェブ拡張機能 (Chrome ストア)", "kgstudio.wallet.config.file-type-supported": "対応ファイルタイプ:JPG、PNG。最大サイズ:10 MB", "kgstudio.wallet.config.follow-guideline": "フォローしてください", "kgstudio.wallet.config.get-started-description": "「はじめに」画面に表示される画像。ブランドロゴの使用をお勧めします。", "kgstudio.wallet.config.get-started-title": "「はじめに」画像", "kgstudio.wallet.config.ios": "iOS (アプリストア)", "kgstudio.wallet.config.label.add": "追加", "kgstudio.wallet.config.label.all-chains": "すべてのチェーン", "kgstudio.wallet.config.label.all-evm-chains": "すべての EVM チェーン", "kgstudio.wallet.config.label.currency": "通貨 (必須)", "kgstudio.wallet.config.label.custom": "カスタム", "kgstudio.wallet.config.label.custom-list": "DAppリストをカスタマイズ", "kgstudio.wallet.config.label.custom-token": "カスタムトークン", "kgstudio.wallet.config.label.default-list": "クリプトゴーセレクテッド（ユニスワップ、オープンシー、スシスワップ、レアリブル、デューンなど）", "kgstudio.wallet.config.label.defi": "デファイ", "kgstudio.wallet.config.label.email": "電子メール", "kgstudio.wallet.config.label.explore-dapp-browser": "エクスプローラ-DAppブラウザ", "kgstudio.wallet.config.label.kyc-user-verification": "KYC: ユーザー認証", "kgstudio.wallet.config.label.nft": "NFT", "kgstudio.wallet.config.label.nft-rewards": "NFTリワード", "kgstudio.wallet.config.label.nft-sell": "NFT 販売（OpenSea）", "kgstudio.wallet.config.label.phone": "電話", "kgstudio.wallet.config.label.preview": "プレビュー", "kgstudio.wallet.config.label.pro": "プロ", "kgstudio.wallet.config.label.show-poap": "POAP を表示", "kgstudio.wallet.config.label.swap": "暗号通貨スワップ", "kgstudio.wallet.config.processing-settings": "しばらくお待ちください。設定を処理しています", "kgstudio.wallet.config.promote-banner.desc": "アプリの公開後は、いつでもバナーを更新できます。今すぐバナーを設定する必要がない場合は、この質問は飛ばしてください。", "kgstudio.wallet.config.promote-banner.title": "宣伝用バナー", "kgstudio.wallet.config.publish-settings-confirm-title": "パブリッシュ設定データが確認されました。今すぐ制作用に提出しますか?", "kgstudio.wallet.config.publish-settings-description": "KryptoGO チームは、アプリの設定に基づいてすぐにウォレットを作成し、公開データとともにアプリストアの審査に提出します。修正を必要とせずにすべてが順調に進めば、約14営業日でストアに公開されます。(今すぐ制作用に提出しない場合は、後でウォレットプロジェクトページの「送信」をクリックしてください)", "kgstudio.wallet.config.recommended-size": "推奨サイズ:1024 x 1024 ピクセル", "kgstudio.wallet.config.retry-or-contact": "後でもう一度試すか、カスタマーサービスに連絡してください", "kgstudio.wallet.config.scanInstruction": "KryptoGo Walletでスキャナーを開き、QRコードをスキャンしてローカルプレビューを行います", "kgstudio.wallet.config.scanToDemo.title1": "スキャンしてデモへ", "kgstudio.wallet.config.scanToDemo.title2": "クリプトゴーウォレットに", "kgstudio.wallet.config.shelf-platform-title": "ローンチするプラットフォーム", "kgstudio.wallet.config.splash-file-type-supported": "対応ファイルタイプ:JPG、PNG。最大サイズ:10 MB", "kgstudio.wallet.config.splash-recommended-size": "推奨サイズ:1080 x 1920 ピクセル", "kgstudio.wallet.config.splash-screen-title": "スプラッシュスクリーン", "kgstudio.wallet.config.steps.check": "チェック", "kgstudio.wallet.config.steps.contact": "連絡先", "kgstudio.wallet.config.steps.explorer": "ブラウザ", "kgstudio.wallet.config.steps.explorer-hint": "ユーザーはエクスプローラーを介してさまざまなDAppに接続できます。ユーザーに推奨するDAppのリストをカスタマイズしたり、広告やガイダンス用のバナーを追加したりできます。アプリが公開された後は、これらの設定をいつでも変更できますのでご安心ください。", "kgstudio.wallet.config.steps.feature": "機能", "kgstudio.wallet.config.steps.theme": "[テーマ]", "kgstudio.wallet.config.store-display-info": "アプリストアの表示データを設定する", "kgstudio.wallet.config.submission-description": "KryptoGoチームは、アプリの設定に基づいてすぐにウォレットを作成し、アプリストアの審査に提出します。修正を必要とせずにすべてが順調に進めば、約14営業日でストアに公開されます。", "kgstudio.wallet.config.submission-failed": "送信に失敗しました!", "kgstudio.wallet.config.submission-failed-description": "送信できませんでした。後でもう一度試してください", "kgstudio.wallet.config.submission-in-progress": "ウォレットアプリの送信中...", "kgstudio.wallet.config.submission-in-progress-description": "しばらくお待ちください。送信中です...", "kgstudio.wallet.config.submission-successful": "送信に成功しました！", "kgstudio.wallet.config.submit-failed": "アプリ設定の送信に失敗しました", "kgstudio.wallet.config.submit-later": "後で送信", "kgstudio.wallet.config.submit-success": "アプリの設定が送信されました。公開設定を続行しますか?", "kgstudio.wallet.config.submitting-app-settings": "アプリ設定を送信中...", "kgstudio.wallet.config.tabs.dex": "デックス", "kgstudio.wallet.config.tabs.hot": "ホット", "kgstudio.wallet.config.tabs.nft": "NFT", "kgstudio.wallet.config.title.dapp-list": "DAppリスト", "kgstudio.wallet.config.title.displayed-asset-type": "表示資産タイプ", "kgstudio.wallet.config.title.displayed-chains": "表示されているブロックチェーン", "kgstudio.wallet.config.title.enable-features": "有効にする機能を選択してください", "kgstudio.wallet.config.title.help-center-url": "ヘルプセンター URL", "kgstudio.wallet.config.title.languages": "言語", "kgstudio.wallet.config.title.login-methods": "ログイン方法", "kgstudio.wallet.config.title.primary-color": "プライマリカラー", "kgstudio.wallet.config.title.privacy-policy-url": "プライバシーポリシー URL", "kgstudio.wallet.config.title.secondary-color": "セカンダリカラー", "kgstudio.wallet.config.title.support-email": "サポートメール", "kgstudio.wallet.config.title.support-info": "サポート情報", "kgstudio.wallet.config.title.terms-condition-url": "利用規約 URL", "kgstudio.wallet.config.title.theme": "[テーマ]", "kgstudio.wallet.customized-dapp-list": "カスタマイズされたDAppリスト", "kgstudio.wallet.dex": "デックス", "kgstudio.wallet.explorer-banner": "エクスプローラーバナー", "kgstudio.wallet.extension.subtitle": "クロームウェブストア", "kgstudio.wallet.extension.title": "[拡張子]", "kgstudio.wallet.feature.explore-dapp": "エクスプローラ-DAppブラウザ", "kgstudio.wallet.feature.nft-rewards": "NFTリワード", "kgstudio.wallet.feature.nft-sell": "NFT 販売", "kgstudio.wallet.feature-settings": "機能設定", "kgstudio.wallet.feature.show-kyc": "本人確認を表示", "kgstudio.wallet.feature.show-poap": "POAP を表示", "kgstudio.wallet.feature.swap": "スワップ", "kgstudio.wallet.google-play.subtitle": "グーグルプレイ", "kgstudio.wallet.google-play.title": "アンドロイド", "kgstudio.wallet.help-center": "ヘルプセンター", "kgstudio.wallet.hot": "ホット", "kgstudio.wallet.ios.subtitle": "アプリストア", "kgstudio.wallet.ios.title": "iOS", "kgstudio.wallet.language.vietnamese": "Tiếng <PERSON>", "kgstudio.wallet.mock.content": "アプリプラットフォームに送信する前に、必ず組織の名前を使用してアプリストアの開発者アカウントを作成してください。アプリストアのポリシーによると、個々の開発者は暗号通貨ウォレットアプリを公開することはできません。詳細については、アプリストアの公開ガイドラインをご覧ください。", "kgstudio.wallet.nft": "NFT", "kgstudio.wallet.privacy-policy": "プライバシーポリシー", "kgstudio.wallet.processing": "しばらくお待ちください。設定を処理しています。", "kgstudio.wallet.project-image": "プロジェクトイメージ", "kgstudio.wallet.project-name": "[プロジェクト名]", "kgstudio.wallet.publish-settings": "パブリッシュ設定", "kgstudio.wallet.setProjectTitle": "「ウォレットを設定」プロジェクト", "kgstudio.wallet.status.draft": "ドラフト", "kgstudio.wallet.status.in-review": "レビュー中", "kgstudio.wallet.status.published": "公開済み", "kgstudio.wallet.supported-chains": "対応チェーン", "kgstudio.wallet.supported-links": "サポート対象リンク", "kgstudio.wallet.support-email": "サポートメール", "kgstudio.wallet.terms-condition": "利用規約", "kgstudio.wallet.theme.primary-color": "プライマリカラー", "kgstudio.wallet.theme.secondary-color": "セカンダリカラー", "kgstudio.wallet.theme.title": "[テーマ]", "kgstudio.wallet.use-recommended-option": "推奨オプションを使用する", "page.page-size-description": "{pageSize}1 ページあたりの行数を表示", "permissions.notification-description": "システムにアクセスする権限を持つユーザーを表示できます。ユーザーを編集したい場合は、システムプロバイダーに連絡してください。", "permissions.title": "ユーザ", "permissions.user-id": "ユーザ ID", "send.by-email": "Eメールで", "send.by-phone-number": "電話番号で", "send.do-not-leave-page": "このページを離れないでください。", "send.loading-hint": "ブロックチェーンが非常に混雑している場合、トランザクションに予想よりも時間がかかることがあります。トランザクションのステータスと詳細を確認するには、Tx Hashをクリックしてください。", "send.over-limit": "オーバー・ザ・リミット", "send.remaining-balance": "今日の残りの振替可能残高: ", "send.remaining-balance-today": "{formattedCurrentLimit}今日の残高はU（{formattedDailyLimit}1日の上限はU）です。", "send.send-confirm-alert": "この取引が確認されると、すぐに有効になり、元に戻すことはできません！ブロックチェーン、金額、受取人が正しいことを確認してください。", "send.send-to": "に送信", "send.title": "資金を送る", "send.to-user": "ユーザーへ", "send.tx-failed": "トランザクション失敗", "send.tx-failed-description": "送金に失敗しました。再試行するか、システム管理者に連絡してください。", "send.tx-in-progress": "トランザクションが進行中です。", "send.tx-success": "トランザクション成功！", "transactions.recipient-placeholder": "電子メール、電話、住所", "transactions.title": "トランザクション履歴", "transactions.user-placeholder": "ID、アドレス", "validation.correct-format": "正しい形式を入力してください", "validation.number-greater-than-zero": "0 より大きい値を入力してください", "validation.phone-or-email-required": "このウォレットに登録したユーザーには、「電子メール」または「電話番号」を使用してのみ資金を送金できます。", "validation.required": "値を入力してください", "validation.sorrect-format": "有効な形式を入力してください。", "validation.valid-email": "有効なメールアドレスを入力してください", "validation.valid-phone": "有効な電話番号を入力してください"}