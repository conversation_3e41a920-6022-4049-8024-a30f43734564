{"name": "kgstudio", "version": "0.0.1", "private": true, "scripts": {"dev:dependency": "turbo dev --ui=stream --filter=kgstudio^...", "test:crypto": "vitest run scripts/crypto.test.ts", "dev": "next dev --turbo", "dev:webpack": "next dev", "dev:test": "next dev --turbo -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf .next", "e2e": "./scripts/run-e2e.sh", "e2e:headless": "./scripts/run-e2e-headless.sh", "test:unit:watch": "vitest", "test:unit": "vitest run", "coverage": "vitest run --coverage", "test": "pnpm run e2e:headless", "typecheck": "NODE_OPTIONS=--max_old_space_size=4096 tsc --noEmit", "i18n:push": "localizely-cli push --reviewed=false", "i18n:push:all": "localizely-cli push --reviewed=false --files \"file[0]=src/locales/en-US.json,locale_code[0]=en-US,file[1]=src/locales/zh-TW.json,locale_code[1]=zh-TW,file[2]=src/locales/zh-CN.json,locale_code[2]=zh-CN,file[3]=src/locales/es.json,locale_code[3]=es,file[4]=src/locales/ja.json,locale_code[4]=ja,file[5]=src/locales/vi-VN.json,locale_code[5]=vi-VN\"", "i18n:pull": "localizely-cli pull"}, "dependencies": {"@hookform/resolvers": "^3.1.0", "@kryptogo/2b": "workspace:*", "@kryptogo/configs": "workspace:*", "@kryptogo/utils": "workspace:*", "@noble/curves": "^1.9.1", "@noble/ed25519": "^2.2.3", "@noble/hashes": "^1.8.0", "@radix-ui/react-collapsible": "^1.0.2", "@radix-ui/react-dialog": "^1.0.3", "@radix-ui/react-navigation-menu": "^1.1.2", "@radix-ui/react-popover": "^1.0.5", "@radix-ui/react-radio-group": "^1.1.2", "@radix-ui/react-separator": "^1.0.2", "@radix-ui/react-slot": "^1.0.1", "@sentry/nextjs": "^8.55.0", "@t3-oss/env-nextjs": "catalog:", "@tailwindcss/container-queries": "^0.1.1", "@tanstack/react-query": "catalog:", "@tanstack/react-query-persist-client": "^4.29.7", "@tanstack/react-table": "^8.17.3", "@types/d3": "^7.4.0", "@types/js-cookie": "^3.0.3", "@types/lodash-es": "catalog:", "@types/multicoin-address-validator": "^0.5.0", "@types/qs": "^6.9.15", "@wagmi/core": "1.0.5", "@walletconnect/core": "2.20.0", "@walletconnect/ethereum-provider": "2.7.0", "@walletconnect/jsonrpc-http-connection": "1.0.6", "@walletconnect/jsonrpc-provider": "1.0.8", "@walletconnect/jsonrpc-utils": "1.0.6", "@walletconnect/jsonrpc-ws-connection": "1.0.9", "@walletconnect/sign-client": "2.6.2", "@walletconnect/universal-provider": "2.20.0", "@walletconnect/utils": "2.6.0", "@web3modal/standalone": "2.2.0", "@zodios/core": "^10.9.6", "@zodios/react": "^10.4.5", "abitype": "0.8.2", "autoprefixer": "catalog:", "axios": "catalog:", "bignumber.js": "^9.1.1", "class-variance-authority": "catalog:", "clsx": "catalog:", "cypress-real-events": "^1.11.0", "d3": "^7.8.5", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "dexie": "catalog:", "export-to-csv": "^1.4.0", "fp-ts": "^2.15.0", "framer-motion": "^12.23.6", "immer": "^10.0.2", "jose": "^5.4.1", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lodash-es": "^4.17.21", "lokijs": "1.x", "lucide-react": "catalog:", "moment": "^2.29.4", "multicoin-address-validator": "^0.5.21", "nanoid": "^4.0.2", "nested-property": "^4.0.0", "next": "15.4.3", "next-intl": "^4.3.4", "next-seo": "^6.0.0", "phone": "^3.1.37", "postcss": "catalog:", "qrcode.react": "^3.1.0", "qs": "^6.12.1", "react": "catalog:", "react-day-picker": "^8.7.1", "react-dom": "catalog:", "react-google-recaptcha-v3": "^1.10.1", "react-hook-form": "catalog:", "react-hotkeys-hook": "^4.4.1", "react-images-uploading": "^3.1.7", "react-intl": "^6.4.2", "react-joyride": "3.0.0-7", "react-toastify": "^9.1.3", "react18-json-view": "^0.2.9", "recharts": "^2.7.3", "server-only": "^0.0.1", "sharp": "^0.34.3", "sonner": "^0.6.2", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss-animate": "catalog:", "ts-pattern": "catalog:", "typescript": "beta", "uuid": "^9.0.0", "wagmi": "1.x", "zod": "catalog:", "zustand": "catalog:"}, "devDependencies": {"@faker-js/faker": "^8.0.2", "@kryptogo/configs": "workspace:*", "@percy/cypress": "^3.1.2", "@tanstack/react-query-devtools": "catalog:", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.0.1", "@types/chai-subset": "^1.3.3", "@types/node": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "@types/uuid": "^9.0.2", "@vitejs/plugin-react": "^4.3.1", "@wagmi/chains": "^1.8.0", "chai-subset": "^1.6.0", "cssnano": "^6.0.1", "cypress-localstorage-commands": "^2.2.3", "cypress-mochawesome-reporter": "^3.5.1", "encoding": "^0.1.13", "eslint-config-custom": "workspace:*", "jsdom": "^25.0.0", "start-server-and-test": "^2.0.0", "tailwind-merge": "catalog:", "vitest": "catalog:", "webpack": "^5.88.1"}}