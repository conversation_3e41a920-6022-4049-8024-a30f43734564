import orgInfoWithId2 from '../../fixtures/organization/info-2.json';

/// <reference types="cypress" />

describe('[UI] Layout - Sidebar', () => {
  beforeEach(() => {
    cy.interceptedAccount();
    cy.interceptedGetLimit();
    cy.interceptedGetTokens();
    cy.setLoggedIn({
      modules: {
        user_360: ['data', 'engage', 'audience'],
        asset_pro: ['market', 'send_token', 'transaction_history'],
      },
      permissions: [
        {
          resource: 'user_360_statistics_compliance',
          action: 'read',
        },
        {
          resource: 'asset_pro_order',
          action: 'read',
        },
        {
          resource: 'transaction',
          action: 'apply',
        },
      ],
    }).then((orgId) => {
      cy.intercept(
        'GET',
        `${Cypress.env('apiBaseUrl')}/studio/organization/*/asset_pro/transfer/pending_history_count`,
        {
          fixture: '/transactions/pending-history-count.json',
        },
      );
    });
  });

  it(`Given:
        - As a studio member
      When:
        - Login successfully
      Then:
        - Should display Sidebar routes: Transfer/ Transaction Histories/ Users
        - Should display Compliance notification count correctly
        - Should display pending Transaction(status: awaiting_approval + awaiting_release) count correctly
      `, () => {
    cy.wait('@getPendinfOrderCount');
    cy.getBySel('sidebar-menu').click();
    cy.getBySel('sidebar-item-\\/asset\\/orders').should('contain', 2);
    cy.getBySel('sidebar').contains('Transfer', { timeout: 5000 }).should('have.attr', 'href', '/en-US/asset/transfer');
    cy.getBySel('sidebar').contains('Transactions').should('have.attr', 'href', '/en-US/asset/transactions');
    cy.getBySel('sidebar').contains('Members').should('have.attr', 'href', '/en-US/setting/team');
  });

  it(`Given:
        - As a studio member
      When:
        - Switch between organizations
      Then:
        - Should display current organization correctly
      `, () => {
    // cy.wait('@getPendinfOrderCount');
    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/2/info`, {
      fixture: '/organization/info-2.json',
    }).as('getOrgInfo2');

    cy.intercept('GET', `${Cypress.env('apiBaseUrl')}/studio/organization/2/me`, {
      fixture: '/organization/me.json',
    }).as('getMe');

    cy.getBySel('sidebar-menu').click();
    cy.getBySel('sidebar').find('button').first().click();
    cy.get('div[role="menuitem"]').eq(1).should('exist').click();
    cy.contains(orgInfoWithId2.data.name).should('exist');
  });
});
