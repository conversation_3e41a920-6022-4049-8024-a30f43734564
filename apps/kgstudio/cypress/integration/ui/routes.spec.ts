/// <reference types="cypress" />

describe('Inspect sidebar & route access behavior', () => {
  beforeEach(() => {
    cy.setLoggedIn({
      modules: {
        user_360: ['engage', 'audience'],
        asset_pro: ['treasury', 'send_token'],
      },
    }).then((orgId) => {
      Cypress.env('orgId', orgId);
    });
  });

  it('Sidebar will show User 360 & AssetPro modules', () => {
    cy.getBySel('sidebar')
      .should('contain.text', 'User 360')
      .and('contain.text', 'All Users')
      .and('contain.text', 'AssetPro')
      .and('contain.text', 'Treasury')
      .and('contain.text', 'Transfer')
      .and('contain.text', 'Overview')
      .and('contain.text', 'Team Members')
      .and('not.contain.text', 'Transactions');
  });

  it(`Given:
        - As a studio user that has no access to User 360's Wallet page
        When:
        - Visit /user360/data/wallet
        Then:
        - Should redirect to not found page`, () => {
    cy.visit('/user360/data/wallet');
    cy.getBySel('not-found').should('contain.text', 'Not Found');
    cy.getBySel('return-link').click();
    cy.location('pathname').should('eq', '/en-US/home/<USER>');
  });
});
