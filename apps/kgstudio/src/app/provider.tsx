'use client';

import { useState } from 'react';
import { WagmiConfig, configureChains, createConfig } from 'wagmi';
import { mainnet, polygon } from 'wagmi/chains';
import { publicProvider } from 'wagmi/providers/public';

import { ToastContainer } from '@/2b/toast';
import { generateStudioQueryClient } from '@/app/_common/lib/react-query';
import { TooltipProvider } from '@kryptogo/2b';
import { QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

const { publicClient, webSocketPublicClient } = configureChains([mainnet, polygon], [publicProvider()]);
const wagmiConfig = createConfig({
  autoConnect: true,
  publicClient,
  webSocketPublicClient,
});

export default function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(generateStudioQueryClient);

  return (
    <QueryClientProvider client={queryClient}>
      <WagmiConfig config={wagmiConfig}>
        <TooltipProvider>
          {children}
          <ReactQueryDevtools initialIsOpen={false} />
          <ToastContainer className="z-50 mb-[14px]" position="bottom-center" closeButton richColors />
        </TooltipProvider>
      </WagmiConfig>
    </QueryClientProvider>
  );
}
