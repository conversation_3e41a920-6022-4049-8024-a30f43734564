import createNextIntlPlugin from 'next-intl/plugin';

import { withSentryConfig } from '@sentry/nextjs';

import './src/env.mjs';

const withNextIntl = createNextIntlPlugin('./src/i18n/request.ts');

/** @type {import('next').NextConfig} */

const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // reactStrictMode: true,
  webpack(config, { dev }) {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
      child_process: false,
    };
    return config;
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'wallet-static.kryptogo.com',
      },
      {
        protocol: 'https',
        hostname: 'wallet-static-dev.kryptogo.com',
      },
      {
        protocol: 'https',
        hostname: 'wallet-static-staging.kryptogo.com',
      },
      {
        protocol: 'https',
        hostname: 'cloudflare-ipfs.com',
      },
      {
        protocol: 'https',
        hostname: 'loremflickr.com',
      },
      {
        protocol: 'https',
        hostname: 'picsum.photos',
      },
      {
        protocol: 'https',
        hostname: 'storage.googleapis.com',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
      },
      {
        protocol: 'https',
        hostname: 'token-icons.s3.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'assets.coingecko.com',
      },
      {
        protocol: 'https',
        hostname: 'static.tronscan.org',
      },
    ],
  },
  async redirects() {
    return [
      {
        source: '/wallet/projects',
        destination: '/wallet/projects/overview',
        permanent: true,
      },
      {
        source: '/user360/engage',
        destination: '/user360/engage/overview',
        permanent: true,
      },
      {
        source: '/user360/audience',
        destination: '/user360/audience/overview',
        permanent: true,
      },
      {
        source: '/user360/data',
        destination: '/user360/data/compliance',
        permanent: true,
      },
    ];
  },
  transpilePackages: ['lucide-react'],
};

const finalConfig = withNextIntl(nextConfig);

// Only use Sentry when not using Turbopack
const isTurbopack = process.env.TURBOPACK === '1' || process.argv.includes('--turbo');

export default isTurbopack
  ? finalConfig
  : withSentryConfig(
      finalConfig,
      {
        // For all available options, see:
        // https://github.com/getsentry/sentry-webpack-plugin#options
        org: 'kryptogo-jr',
        project: process.env.SENTRY_PROJECT,
        authToken: process.env.SENTRY_AUTH_TOKEN,
      },
      {
        // For all available options, see:
        // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/
        // Upload a larger set of source maps for prettier stack traces (increases build time)
        widenClientFileUpload: true,
        // Transpiles SDK to be compatible with IE11 (increases bundle size)
        transpileClientSDK: true,
        // Hides source maps from generated client bundles
        hideSourceMaps: true,
        // Automatically tree-shake Sentry logger statements to reduce bundle size
        disableLogger: true,
      },
    );
