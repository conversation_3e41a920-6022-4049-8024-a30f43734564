import { z } from 'zod';

import { ApiResponseSchema, ApiResponseWithPagingSchema } from '@/app/_common/lib/api';

// Chain ID schema for payment service
export const PaymentChainIdSchema = z.enum(['arb', 'base', 'optimism']);
export type PaymentChainId = z.infer<typeof PaymentChainIdSchema>;

// Payment status schema
export const PaymentStatusSchema = z.enum([
  'pending',
  'success',
  'expired',
  'insufficient_not_refunded',
  'insufficient_refunded',
]);
export type PaymentStatus = z.infer<typeof PaymentStatusSchema>;

// Individual payment intent schema
export const PaymentIntentSchema = z.object({
  payment_intent_id: z.string(),
  client_id: z.string(),
  payment_chain_id: PaymentChainIdSchema,
  payment_address: z.string(),
  token_address: z.string(),
  symbol: z.string(),
  decimals: z.number().positive(),
  crypto_amount: z.string(),
  fiat_amount: z.string(),
  fiat_currency: z.string(),
  payment_deadline: z.number().positive(),
  status: PaymentStatusSchema,
  group_key: z.string().nullable().optional(),
  payment_tx_hash: z.string().nullable().optional(),
  order_data: z.unknown().nullable().optional(),
  callback_url: z.string().url().nullable().optional(),
  received_crypto_amount: z.string().nullable().optional(),
  aggregated_crypto_amount: z.string().nullable().optional(),
  refund_crypto_amount: z.string().nullable().optional(),
  refund_tx_hash: z.string().nullable().optional(),
});
export type PaymentIntent = z.infer<typeof PaymentIntentSchema>;

// List of payment intents response schema
export const PaymentIntentsListSchema = ApiResponseWithPagingSchema(z.array(PaymentIntentSchema));
export type PaymentIntentsList = z.infer<typeof PaymentIntentsListSchema>;

// Create payment intent request schema
export const CreatePaymentIntentRequestSchema = z.object({
  chain_id: PaymentChainIdSchema,
  token_address: z.string(),
  fiat_amount: z.string(),
  fiat_currency: z.string(),
  callback_url: z.string().url().optional(),
  order_data: z.unknown().optional(),
  pay_token: z.string().optional(),
});
export type CreatePaymentIntentRequest = z.infer<typeof CreatePaymentIntentRequestSchema>;

// Create payment intent response schema
export const CreatePaymentIntentResponseSchema = ApiResponseSchema(PaymentIntentSchema);
export type CreatePaymentIntentResponse = z.infer<typeof CreatePaymentIntentResponseSchema>;

// Get payment intent by ID response schema
export const GetPaymentIntentResponseSchema = ApiResponseSchema(PaymentIntentSchema);
export type GetPaymentIntentResponse = z.infer<typeof GetPaymentIntentResponseSchema>;
